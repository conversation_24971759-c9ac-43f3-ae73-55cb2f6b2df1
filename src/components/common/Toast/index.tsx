import React from 'react';
import Toast, { ToastConfig, ToastConfigParams } from 'react-native-toast-message';
import { View, Text, StyleSheet } from 'react-native';
import { colors, typography, spacing } from '../../../styles';

// Toast configuration
export const toastConfig: ToastConfig = {
  success: ({ text1, text2 }: ToastConfigParams<any>) => (
    <View style={[styles.container, styles.success]}>
      <Text style={styles.title}>{text1}</Text>
      {text2 && <Text style={styles.message}>{text2}</Text>}
    </View>
  ),
  
  error: ({ text1, text2 }: ToastConfigParams<any>) => (
    <View style={[styles.container, styles.error]}>
      <Text style={styles.title}>{text1}</Text>
      {text2 && <Text style={styles.message}>{text2}</Text>}
    </View>
  ),
  
  info: ({ text1, text2 }: ToastConfigParams<any>) => (
    <View style={[styles.container, styles.info]}>
      <Text style={styles.title}>{text1}</Text>
      {text2 && <Text style={styles.message}>{text2}</Text>}
    </View>
  ),
  
  warning: ({ text1, text2 }: ToastConfigParams<any>) => (
    <View style={[styles.container, styles.warning]}>
      <Text style={styles.title}>{text1}</Text>
      {text2 && <Text style={styles.message}>{text2}</Text>}
    </View>
  ),
};

// Toast service for easy usage
export class ToastService {
  static show(config: {
    type: 'success' | 'error' | 'info' | 'warning';
    title: string;
    message?: string;
    duration?: number;
  }) {
    Toast.show({
      type: config.type,
      text1: config.title,
      text2: config.message,
      visibilityTime: config.duration || 3000,
      autoHide: true,
      topOffset: 50,
      bottomOffset: 40,
    });
  }
  
  static success(title: string, message?: string) {
    this.show({ type: 'success', title, message });
  }
  
  static error(title: string, message?: string) {
    this.show({ type: 'error', title, message });
  }
  
  static info(title: string, message?: string) {
    this.show({ type: 'info', title, message });
  }
  
  static warning(title: string, message?: string) {
    this.show({ type: 'warning', title, message });
  }
  
  static hide() {
    Toast.hide();
  }
}

const styles = StyleSheet.create({
  container: {
    width: '90%',
    padding: spacing.md,
    borderRadius: 8,
    marginHorizontal: spacing.md,
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.25,
    shadowRadius: 3.84,
    elevation: 5,
  },
  
  success: {
    backgroundColor: colors.success,
  },
  
  error: {
    backgroundColor: colors.error,
  },
  
  info: {
    backgroundColor: colors.info,
  },
  
  warning: {
    backgroundColor: colors.warning,
  },
  
  title: {
    fontSize: typography.fontSize.base,
    fontWeight: typography.fontWeight.semibold,
    color: colors.background,
    marginBottom: spacing.xs,
  },
  
  message: {
    fontSize: typography.fontSize.sm,
    color: colors.background,
    opacity: 0.9,
  },
});

export default Toast;
