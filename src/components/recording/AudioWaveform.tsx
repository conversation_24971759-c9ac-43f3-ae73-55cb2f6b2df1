import React, { useEffect, useRef, useState } from 'react';
import { View, StyleSheet, Animated, Dimensions } from 'react-native';
import { colors } from '../../styles';

interface AudioWaveformProps {
  isRecording: boolean;
  audioLevel?: number; // 0-1 范围的音频电平
}

const { width: screenWidth } = Dimensions.get('window');
const WAVE_WIDTH = screenWidth * 0.6; // 波形宽度
const WAVE_HEIGHT = 60; // 波形高度
const BAR_COUNT = 30; // 波形条数
const BAR_WIDTH = WAVE_WIDTH / BAR_COUNT - 2; // 每个条的宽度

const AudioWaveform: React.FC<AudioWaveformProps> = ({
  isRecording,
  audioLevel = 0.5,
}) => {
  const animatedValues = useRef(
    Array.from({ length: BAR_COUNT }, () => new Animated.Value(0.1))
  ).current;
  const [waveformData, setWaveformData] = useState<number[]>(
    Array(BAR_COUNT).fill(0.1)
  );

  useEffect(() => {
    let interval: NodeJS.Timeout;
    if (isRecording) {
      interval = setInterval(() => {
        // 基于音频电平生成更自然的波形
        const newWaveform = Array.from({ length: BAR_COUNT }, (_, index) => {
          // 使用音频电平作为基础高度
          const baseHeight = Math.max(0.1, audioLevel * 0.8);
          // 添加随机变化，但限制在audioLevel范围内
          const variation = (Math.random() - 0.5) * 0.3 * audioLevel;
          // 中间的条稍微高一些，模拟真实的音频波形
          const centerFactor =
            1 + 0.3 * Math.sin((index / BAR_COUNT) * Math.PI);

          return Math.max(
            0.1,
            Math.min(1.0, (baseHeight + variation) * centerFactor)
          );
        });
        setWaveformData(newWaveform);
      }, 100);
    } else {
      // 停止录音时，重置波形为静态状态
      setWaveformData(Array(BAR_COUNT).fill(0.1));
    }

    return () => {
      if (interval) clearInterval(interval);
    };
  }, [isRecording, audioLevel]);

  useEffect(() => {
    // 当波形数据更新时，创建动画
    if (isRecording) {
      const animations = animatedValues.map((animatedValue, index) => {
        return Animated.timing(animatedValue, {
          toValue: waveformData[index],
          duration: 150,
          useNativeDriver: false,
        });
      });

      Animated.parallel(animations).start();
    } else {
      // 停止录音时，将所有波形归零
      const resetAnimations = animatedValues.map((animatedValue) =>
        Animated.timing(animatedValue, {
          toValue: 0.1,
          duration: 300,
          useNativeDriver: false,
        })
      );

      Animated.parallel(resetAnimations).start();
    }
  }, [waveformData, isRecording]);

  if (!isRecording) {
    return null;
  }

  return (
    <View style={styles.container}>
      <View style={styles.waveform}>
        {animatedValues.map((animatedValue, index) => (
          <Animated.View
            key={index}
            style={[
              styles.bar,
              {
                width: BAR_WIDTH,
                height: animatedValue.interpolate({
                  inputRange: [0, 1],
                  outputRange: [4, WAVE_HEIGHT],
                }),
              },
            ]}
          />
        ))}
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    alignItems: 'center',
    justifyContent: 'center',
    height: WAVE_HEIGHT + 20,
  },
  waveform: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    height: WAVE_HEIGHT,
    width: WAVE_WIDTH,
  },
  bar: {
    backgroundColor: colors.primary,
    marginHorizontal: 1,
    borderRadius: 2,
    opacity: 0.8,
  },
});

export default AudioWaveform;
