import { create } from 'zustand';
import { persist, createJSONStorage } from 'zustand/middleware';
import AsyncStorage from '@react-native-async-storage/async-storage';
import { KnowledgeDocument, KnowledgeCategory } from '../types';

export interface KnowledgeState {
  // 知识库状态
  documents: KnowledgeDocument[];
  categories: KnowledgeCategory[];
  currentCategory: KnowledgeCategory | null;
  isLoading: boolean;
  
  // Actions
  setDocuments: (documents: KnowledgeDocument[]) => void;
  addDocument: (document: KnowledgeDocument) => void;
  updateDocument: (id: string, updates: Partial<KnowledgeDocument>) => void;
  deleteDocument: (id: string) => void;
  
  setCategories: (categories: KnowledgeCategory[]) => void;
  addCategory: (category: KnowledgeCategory) => void;
  updateCategory: (id: string, updates: Partial<KnowledgeCategory>) => void;
  deleteCategory: (id: string) => void;
  setCurrentCategory: (category: KnowledgeCategory | null) => void;
  setLoading: (loading: boolean) => void;
}

export const useKnowledgeStore = create<KnowledgeState>()(
  persist(
    (set, get) => ({
      // 初始状态
      documents: [],
      categories: [],
      currentCategory: null,
      isLoading: false,
      
      // Document Actions
      setDocuments: (documents) => set({ documents }),
      
      addDocument: (document) => {
        const { documents } = get();
        set({ documents: [document, ...documents] });
      },
      
      updateDocument: (id, updates) => {
        const { documents } = get();
        const updatedDocuments = documents.map((doc) =>
          doc.id === id ? { ...doc, ...updates } : doc
        );
        set({ documents: updatedDocuments });
      },
      
      deleteDocument: (id) => {
        const { documents } = get();
        const filteredDocuments = documents.filter((doc) => doc.id !== id);
        set({ documents: filteredDocuments });
      },
      
      // Category Actions
      setCategories: (categories) => set({ categories }),
      
      addCategory: (category) => {
        const { categories } = get();
        set({ categories: [...categories, category] });
      },
      
      updateCategory: (id, updates) => {
        const { categories } = get();
        const updatedCategories = categories.map((cat) =>
          cat.id === id ? { ...cat, ...updates } : cat
        );
        set({ categories: updatedCategories });
      },
      
      deleteCategory: (id) => {
        const { categories } = get();
        const filteredCategories = categories.filter((cat) => cat.id !== id);
        set({ categories: filteredCategories });
      },
      
      setCurrentCategory: (category) => set({ currentCategory: category }),
      
      setLoading: (loading) => set({ isLoading: loading }),
    }),
    {
      name: 'knowledge-storage',
      storage: createJSONStorage(() => AsyncStorage),
    }
  )
);
