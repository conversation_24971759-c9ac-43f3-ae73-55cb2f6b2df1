import { create } from 'zustand';
import { persist, createJSONStorage } from 'zustand/middleware';
import AsyncStorage from '@react-native-async-storage/async-storage';
import { Recording } from '../types';

export interface RecordingState {
  // 录音状态
  recordings: Recording[];
  currentRecording: Recording | null;
  isRecording: boolean;

  // Actions
  setRecordings: (recordings: Recording[]) => void;
  addRecording: (recording: Recording) => void;
  updateRecording: (id: string, updates: Partial<Recording>) => void;
  deleteRecording: (id: string) => void;
  setCurrentRecording: (recording: Recording | null) => void;
  setIsRecording: (isRecording: boolean) => void;
}

export const useRecordingStore = create<RecordingState>()(
  persist(
    (set, get) => ({
      // 初始状态
      recordings: [],
      currentRecording: null,
      isRecording: false,

      // Actions
      setRecordings: (recordings) => set({ recordings }),

      addRecording: (recording) => {
        const { recordings } = get();
        set({ recordings: [recording, ...recordings] });
      },

      updateRecording: (id, updates) => {
        const { recordings } = get();
        const updatedRecordings = recordings.map((recording) =>
          recording.id === id ? { ...recording, ...updates } : recording
        );
        set({ recordings: updatedRecordings });
      },

      deleteRecording: (id) => {
        const { recordings } = get();
        const filteredRecordings = recordings.filter(
          (recording) => recording.id !== id
        );
        set({ recordings: filteredRecordings });
      },

      setCurrentRecording: (recording) => set({ currentRecording: recording }),
      setIsRecording: (isRecording) => set({ isRecording }),
    }),
    {
      name: 'recording-storage',
      storage: createJSONStorage(() => AsyncStorage),
      partialize: (state) => ({
        recordings: state.recordings.map((recording) => ({
          ...recording,
          createdAt: recording.createdAt.toISOString(),
          updatedAt: recording.updatedAt.toISOString(),
        })),
      }),
      onRehydrateStorage: () => (state) => {
        if (state) {
          state.recordings = state.recordings.map((recording) => ({
            ...recording,
            createdAt: new Date(recording.createdAt),
            updatedAt: new Date(recording.updatedAt),
          }));
        }
      },
    }
  )
);
