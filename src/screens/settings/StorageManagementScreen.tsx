import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  TouchableOpacity,
  Alert,
  ActivityIndicator,
} from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import { Ionicons } from '@expo/vector-icons';
import { colors, spacing, typography } from '@/styles';
import Toast from 'react-native-toast-message';

interface StorageItem {
  id: string;
  title: string;
  description: string;
  size: string;
  icon: keyof typeof Ionicons.glyphMap;
  color: string;
  clearable: boolean;
}

export const StorageManagementScreen: React.FC = () => {
  const [storageItems, setStorageItems] = useState<StorageItem[]>([]);
  const [totalUsage, setTotalUsage] = useState('0 MB');
  const [isLoading, setIsLoading] = useState(true);
  const [isClearing, setIsClearing] = useState(false);

  useEffect(() => {
    calculateStorageUsage();
  }, []);

  const formatBytes = (bytes: number): string => {
    if (bytes === 0) return '0 B';
    const k = 1024;
    const sizes = ['B', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  };

  const calculateStorageUsage = async () => {
    setIsLoading(true);
    try {
      // 模拟计算各类数据的存储占用
      const items: StorageItem[] = [
        {
          id: 'recordings',
          title: '录音文件',
          description: '所有录音文件和转写文本',
          size: formatBytes(52428800), // 50MB
          icon: 'mic',
          color: colors.primary,
          clearable: true,
        },
        {
          id: 'knowledge',
          title: '知识库文档',
          description: '上传的文档和音频文件',
          size: formatBytes(31457280), // 30MB
          icon: 'document-text',
          color: '#FF9500',
          clearable: true,
        },
        {
          id: 'ai_cache',
          title: 'AI缓存',
          description: 'AI生成内容和模型缓存',
          size: formatBytes(10485760), // 10MB
          icon: 'sparkles',
          color: '#5856D6',
          clearable: true,
        },
        {
          id: 'images',
          title: '图片缓存',
          description: '头像和其他图片文件',
          size: formatBytes(5242880), // 5MB
          icon: 'image',
          color: '#34C759',
          clearable: true,
        },
        {
          id: 'app_data',
          title: '应用数据',
          description: '设置、偏好和用户数据',
          size: formatBytes(1048576), // 1MB
          icon: 'settings',
          color: '#007AFF',
          clearable: false,
        },
        {
          id: 'temp',
          title: '临时文件',
          description: '可安全删除的临时数据',
          size: formatBytes(2097152), // 2MB
          icon: 'time',
          color: '#8E8E93',
          clearable: true,
        },
      ];

      setStorageItems(items);

      // 计算总使用量
      const total = items.reduce((sum, item) => {
        const sizeMatch = item.size.match(/(\d+\.?\d*)/);
        const size = sizeMatch ? parseFloat(sizeMatch[1]) : 0;
        const unit = item.size.replace(/[\d.]/g, '').trim();
        const multiplier =
          unit === 'GB'
            ? 1024
            : unit === 'MB'
              ? 1
              : unit === 'KB'
                ? 0.001
                : 0.000001;
        return sum + size * multiplier;
      }, 0);

      setTotalUsage(formatBytes(total * 1024 * 1024));
    } catch (error) {
      console.error('计算存储使用量失败:', error);
    } finally {
      setIsLoading(false);
    }
  };

  const handleClearItem = async (item: StorageItem) => {
    Alert.alert(
      '清理确认',
      `确定要清理"${item.title}"吗？\n这将释放 ${item.size} 的存储空间。`,
      [
        { text: '取消', style: 'cancel' },
        {
          text: '清理',
          style: 'destructive',
          onPress: async () => {
            setIsClearing(true);
            try {
              // 模拟清理过程
              await new Promise((resolve) => setTimeout(resolve, 1500));

              Toast.show({
                type: 'success',
                text1: '清理成功',
                text2: `已释放 ${item.size} 存储空间`,
              });

              // 重新计算存储使用量
              await calculateStorageUsage();
            } catch (error) {
              Toast.show({
                type: 'error',
                text1: '清理失败',
                text2: '请稍后重试',
              });
            } finally {
              setIsClearing(false);
            }
          },
        },
      ]
    );
  };

  const handleClearAll = () => {
    const clearableItems = storageItems.filter((item) => item.clearable);
    const totalClearableSize = clearableItems.reduce((sum, item) => {
      const sizeMatch = item.size.match(/(\d+\.?\d*)/);
      const size = sizeMatch ? parseFloat(sizeMatch[1]) : 0;
      const unit = item.size.replace(/[\d.]/g, '').trim();
      const multiplier =
        unit === 'GB'
          ? 1024
          : unit === 'MB'
            ? 1
            : unit === 'KB'
              ? 0.001
              : 0.000001;
      return sum + size * multiplier;
    }, 0);

    Alert.alert(
      '清理所有缓存',
      `确定要清理所有可清理的缓存吗？\n这将释放约 ${formatBytes(totalClearableSize * 1024 * 1024)} 的存储空间。`,
      [
        { text: '取消', style: 'cancel' },
        {
          text: '清理全部',
          style: 'destructive',
          onPress: async () => {
            setIsClearing(true);
            try {
              await new Promise((resolve) => setTimeout(resolve, 2000));

              Toast.show({
                type: 'success',
                text1: '清理成功',
                text2: `已释放 ${formatBytes(totalClearableSize * 1024 * 1024)} 存储空间`,
              });

              await calculateStorageUsage();
            } catch (error) {
              Toast.show({
                type: 'error',
                text1: '清理失败',
                text2: '请稍后重试',
              });
            } finally {
              setIsClearing(false);
            }
          },
        },
      ]
    );
  };

  const renderStorageItem = (item: StorageItem) => (
    <View key={item.id} style={styles.storageItem}>
      <View style={[styles.itemIcon, { backgroundColor: item.color + '20' }]}>
        <Ionicons name={item.icon} size={24} color={item.color} />
      </View>
      <View style={styles.itemInfo}>
        <Text style={styles.itemTitle}>{item.title}</Text>
        <Text style={styles.itemDescription}>{item.description}</Text>
        <Text style={styles.itemSize}>{item.size}</Text>
      </View>
      {item.clearable && (
        <TouchableOpacity
          style={styles.clearButton}
          onPress={() => handleClearItem(item)}
          disabled={isClearing}
        >
          <Text style={styles.clearButtonText}>清理</Text>
        </TouchableOpacity>
      )}
    </View>
  );

  if (isLoading) {
    return (
      <SafeAreaView style={styles.container}>
        <View style={styles.loadingContainer}>
          <ActivityIndicator size="large" color={colors.primary} />
          <Text style={styles.loadingText}>正在分析存储使用情况...</Text>
        </View>
      </SafeAreaView>
    );
  }

  return (
    <SafeAreaView style={styles.container}>
      <ScrollView
        style={styles.scrollView}
        showsVerticalScrollIndicator={false}
      >
        {/* 头部 */}
        <View style={styles.header}>
          <Text style={styles.title}>存储管理</Text>
          <Text style={styles.subtitle}>管理应用存储空间</Text>
        </View>

        {/* 存储概览 */}
        <View style={styles.overviewCard}>
          <View style={styles.overviewHeader}>
            <Text style={styles.overviewTitle}>存储使用情况</Text>
            <TouchableOpacity onPress={calculateStorageUsage}>
              <Ionicons name="refresh" size={20} color={colors.primary} />
            </TouchableOpacity>
          </View>
          <Text style={styles.totalUsage}>{totalUsage}</Text>
          <Text style={styles.totalUsageLabel}>已使用空间</Text>

          <TouchableOpacity
            style={styles.clearAllButton}
            onPress={handleClearAll}
            disabled={isClearing}
          >
            {isClearing ? (
              <ActivityIndicator size="small" color="#FFFFFF" />
            ) : (
              <>
                <Ionicons name="trash" size={20} color="#FFFFFF" />
                <Text style={styles.clearAllButtonText}>清理所有缓存</Text>
              </>
            )}
          </TouchableOpacity>
        </View>

        {/* 存储详情 */}
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>存储详情</Text>
          <View style={styles.storageList}>
            {storageItems.map(renderStorageItem)}
          </View>
        </View>

        {/* 提示信息 */}
        <View style={styles.tips}>
          <Text style={styles.tipsTitle}>存储管理提示</Text>
          <View style={styles.tipItem}>
            <Ionicons
              name="information-circle"
              size={16}
              color={colors.text.secondary}
            />
            <Text style={styles.tipText}>
              清理缓存不会删除您的个人数据和设置
            </Text>
          </View>
          <View style={styles.tipItem}>
            <Ionicons
              name="shield-checkmark"
              size={16}
              color={colors.text.secondary}
            />
            <Text style={styles.tipText}>
              录音文件和知识库文档清理后将无法恢复，请谨慎操作
            </Text>
          </View>
          <View style={styles.tipItem}>
            <Ionicons
              name="cloud-upload"
              size={16}
              color={colors.text.secondary}
            />
            <Text style={styles.tipText}>建议定期备份重要数据到云端</Text>
          </View>
        </View>
      </ScrollView>
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: colors.background.primary,
  },
  scrollView: {
    flex: 1,
  },
  loadingContainer: {
    flex: 1,
    alignItems: 'center',
    justifyContent: 'center',
  },
  loadingText: {
    marginTop: spacing.md,
    fontSize: typography.fontSize.sm,
    color: colors.text.secondary,
  },
  header: {
    paddingHorizontal: spacing.lg,
    paddingVertical: spacing.md,
    marginBottom: spacing.md,
  },
  title: {
    fontSize: typography.fontSize.xxl,
    fontWeight: typography.fontWeight.bold as any,
    color: colors.text.primary,
    marginBottom: spacing.xs,
  },
  subtitle: {
    fontSize: typography.fontSize.sm,
    color: colors.text.secondary,
  },
  overviewCard: {
    backgroundColor: colors.primary,
    marginHorizontal: spacing.lg,
    borderRadius: 16,
    padding: spacing.lg,
    marginBottom: spacing.xl,
  },
  overviewHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: spacing.md,
  },
  overviewTitle: {
    fontSize: typography.fontSize.md,
    fontWeight: typography.fontWeight.medium as any,
    color: '#FFFFFF',
  },
  totalUsage: {
    fontSize: 36,
    fontWeight: typography.fontWeight.bold as any,
    color: '#FFFFFF',
    marginBottom: spacing.xs,
  },
  totalUsageLabel: {
    fontSize: typography.fontSize.sm,
    color: '#FFFFFF80',
    marginBottom: spacing.lg,
  },
  clearAllButton: {
    backgroundColor: '#FFFFFF20',
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: spacing.sm,
    borderRadius: 8,
    gap: spacing.xs,
  },
  clearAllButtonText: {
    fontSize: typography.fontSize.md,
    fontWeight: typography.fontWeight.medium as any,
    color: '#FFFFFF',
  },
  section: {
    marginBottom: spacing.xl,
  },
  sectionTitle: {
    fontSize: typography.fontSize.sm,
    fontWeight: typography.fontWeight.medium as any,
    color: colors.text.secondary,
    marginBottom: spacing.sm,
    marginHorizontal: spacing.lg,
    textTransform: 'uppercase',
  },
  storageList: {
    backgroundColor: colors.background.secondary,
    marginHorizontal: spacing.lg,
    borderRadius: 12,
    overflow: 'hidden',
  },
  storageItem: {
    flexDirection: 'row',
    alignItems: 'center',
    padding: spacing.md,
    borderBottomWidth: 1,
    borderBottomColor: colors.border,
  },
  itemIcon: {
    width: 48,
    height: 48,
    borderRadius: 12,
    alignItems: 'center',
    justifyContent: 'center',
    marginRight: spacing.md,
  },
  itemInfo: {
    flex: 1,
  },
  itemTitle: {
    fontSize: typography.fontSize.md,
    fontWeight: typography.fontWeight.medium as any,
    color: colors.text.primary,
    marginBottom: spacing.xs / 2,
  },
  itemDescription: {
    fontSize: typography.fontSize.xs,
    color: colors.text.secondary,
    marginBottom: spacing.xs / 2,
  },
  itemSize: {
    fontSize: typography.fontSize.sm,
    fontWeight: typography.fontWeight.medium as any,
    color: colors.primary,
  },
  clearButton: {
    paddingHorizontal: spacing.md,
    paddingVertical: spacing.sm,
    borderRadius: 6,
    backgroundColor: colors.error + '10',
  },
  clearButtonText: {
    fontSize: typography.fontSize.sm,
    fontWeight: typography.fontWeight.medium as any,
    color: colors.error,
  },
  tips: {
    paddingHorizontal: spacing.lg,
    paddingBottom: spacing.xl,
  },
  tipsTitle: {
    fontSize: typography.fontSize.md,
    fontWeight: typography.fontWeight.semibold as any,
    color: colors.text.primary,
    marginBottom: spacing.md,
  },
  tipItem: {
    flexDirection: 'row',
    alignItems: 'flex-start',
    marginBottom: spacing.sm,
    gap: spacing.xs,
  },
  tipText: {
    fontSize: typography.fontSize.xs,
    color: colors.text.secondary,
    flex: 1,
  },
});
