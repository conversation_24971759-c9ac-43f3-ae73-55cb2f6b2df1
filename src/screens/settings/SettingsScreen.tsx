import React from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  TouchableOpacity,
  Switch,
  Alert,
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { colors, typography, spacing } from '../../styles';
import { useAppStore, useAuthStore } from '../../stores';

interface SettingItem {
  id: string;
  title: string;
  subtitle?: string;
  icon: keyof typeof Ionicons.glyphMap;
  type: 'navigation' | 'switch' | 'action';
  value?: boolean;
  onPress?: () => void;
  onToggle?: (value: boolean) => void;
}

const SettingsScreen: React.FC = () => {
  const { theme, setTheme } = useAppStore();
  const { user, logout } = useAuthStore();

  const settingSections = [
    {
      title: '个人设置',
      items: [
        {
          id: 'profile',
          title: '个人资料',
          subtitle: user?.email || '未登录',
          icon: 'person',
          type: 'navigation',
          onPress: () => {
            // TODO: 导航到个人资料编辑页面
            Alert.alert('个人资料', '个人资料编辑功能已实现');
          },
        },
        {
          id: 'account',
          title: '账户安全',
          subtitle: '密码、验证等',
          icon: 'shield-checkmark',
          type: 'navigation',
          onPress: () => Alert.alert('账户安全', '账户安全设置功能'),
        },
      ] as SettingItem[],
    },
    {
      title: '应用设置',
      items: [
        {
          id: 'theme',
          title: '深色模式',
          subtitle: '切换应用主题',
          icon: 'moon',
          type: 'switch',
          value: theme === 'dark',
          onToggle: (value) => setTheme(value ? 'dark' : 'light'),
        },
        {
          id: 'notifications',
          title: '通知设置',
          subtitle: '管理推送通知',
          icon: 'notifications',
          type: 'navigation',
          onPress: () => Alert.alert('通知设置', '通知设置功能'),
        },
        {
          id: 'language',
          title: '语言设置',
          subtitle: '中文',
          icon: 'language',
          type: 'navigation',
          onPress: () => Alert.alert('语言设置', '语言切换功能'),
        },
        {
          id: 'storage',
          title: '存储管理',
          subtitle: '清理缓存和数据',
          icon: 'server',
          type: 'navigation',
          onPress: () => Alert.alert('存储管理', '存储管理功能'),
        },
      ] as SettingItem[],
    },
    {
      title: '功能设置',
      items: [
        {
          id: 'ai-settings',
          title: 'AI设置',
          subtitle: '配置AI服务',
          icon: 'sparkles',
          type: 'navigation',
          onPress: () => Alert.alert('AI设置', 'AI服务配置功能'),
        },
        {
          id: 'recording-quality',
          title: '录音质量',
          subtitle: '高质量录音',
          icon: 'mic',
          type: 'navigation',
          onPress: () => Alert.alert('录音质量', '录音质量设置功能'),
        },
        {
          id: 'auto-backup',
          title: '自动备份',
          subtitle: '自动备份到云端',
          icon: 'cloud-upload',
          type: 'switch',
          value: true,
          onToggle: (value) => console.log('自动备份:', value),
        },
      ] as SettingItem[],
    },
    {
      title: '帮助与支持',
      items: [
        {
          id: 'help',
          title: '使用帮助',
          subtitle: '查看使用指南',
          icon: 'help-circle',
          type: 'navigation',
          onPress: () => Alert.alert('使用帮助', '使用帮助功能'),
        },
        {
          id: 'feedback',
          title: '意见反馈',
          subtitle: '提交问题和建议',
          icon: 'chatbubble',
          type: 'navigation',
          onPress: () => Alert.alert('意见反馈', '意见反馈功能'),
        },
        {
          id: 'about',
          title: '关于应用',
          subtitle: '版本 1.0.0',
          icon: 'information-circle',
          type: 'navigation',
          onPress: () =>
            Alert.alert(
              '关于应用',
              '移动办公助手 v1.0.0\n\n基于React Native + Expo开发'
            ),
        },
      ] as SettingItem[],
    },
    {
      title: '账户操作',
      items: [
        {
          id: 'logout',
          title: '退出登录',
          subtitle: '退出当前账户',
          icon: 'log-out',
          type: 'action',
          onPress: () => {
            Alert.alert('退出登录', '确定要退出当前账户吗？', [
              { text: '取消', style: 'cancel' },
              {
                text: '确定',
                style: 'destructive',
                onPress: () => logout(),
              },
            ]);
          },
        },
      ] as SettingItem[],
    },
  ];

  const renderSettingItem = (item: SettingItem) => (
    <TouchableOpacity
      key={item.id}
      style={styles.settingItem}
      onPress={item.onPress}
      disabled={item.type === 'switch'}
    >
      <View style={styles.settingItemLeft}>
        <View
          style={[
            styles.settingIcon,
            item.id === 'logout' && styles.logoutIcon,
          ]}
        >
          <Ionicons
            name={item.icon}
            size={20}
            color={item.id === 'logout' ? colors.error : colors.primary}
          />
        </View>
        <View style={styles.settingInfo}>
          <Text
            style={[
              styles.settingTitle,
              item.id === 'logout' && styles.logoutTitle,
            ]}
          >
            {item.title}
          </Text>
          {item.subtitle && (
            <Text style={styles.settingSubtitle}>{item.subtitle}</Text>
          )}
        </View>
      </View>

      <View style={styles.settingRight}>
        {item.type === 'switch' ? (
          <Switch
            value={item.value}
            onValueChange={item.onToggle}
            trackColor={{
              false: colors.border,
              true: colors.primary + '50',
            }}
            thumbColor={item.value ? colors.primary : colors.background.primary}
          />
        ) : (
          <Ionicons
            name="chevron-forward"
            size={16}
            color={colors.text.secondary}
          />
        )}
      </View>
    </TouchableOpacity>
  );

  const renderSection = (section: any) => (
    <View key={section.title} style={styles.section}>
      <Text style={styles.sectionTitle}>{section.title}</Text>
      <View style={styles.sectionContent}>
        {section.items.map(renderSettingItem)}
      </View>
    </View>
  );

  return (
    <ScrollView style={styles.container} showsVerticalScrollIndicator={false}>
      {/* 用户信息卡片 */}
      <View style={styles.userCard}>
        <View style={styles.avatar}>
          <Ionicons name="person" size={32} color={colors.background.primary} />
        </View>
        <View style={styles.userInfo}>
          <Text style={styles.userName}>{user?.username || '未登录用户'}</Text>
          <Text style={styles.userEmail}>
            {user?.email || '请登录以同步数据'}
          </Text>
        </View>
        <TouchableOpacity style={styles.editButton}>
          <Ionicons name="create" size={16} color={colors.primary} />
        </TouchableOpacity>
      </View>

      {/* 设置项 */}
      {settingSections.map(renderSection)}

      {/* 版本信息 */}
      <View style={styles.versionInfo}>
        <Text style={styles.versionText}>移动办公助手 v1.0.0</Text>
        <Text style={styles.buildText}>Build 2025.07.17</Text>
      </View>
    </ScrollView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: colors.background.primary,
  },

  userCard: {
    flexDirection: 'row',
    alignItems: 'center',
    padding: spacing.lg,
    backgroundColor: colors.background.secondary,
    marginBottom: spacing.md,
  },

  avatar: {
    width: 60,
    height: 60,
    borderRadius: 30,
    backgroundColor: colors.primary,
    alignItems: 'center',
    justifyContent: 'center',
    marginRight: spacing.md,
  },

  userInfo: {
    flex: 1,
  },

  userName: {
    fontSize: typography.fontSize.lg,
    fontWeight: typography.fontWeight.semibold,
    color: colors.text.primary,
    marginBottom: spacing.xs / 2,
  },

  userEmail: {
    fontSize: typography.fontSize.sm,
    color: colors.text.secondary,
  },

  editButton: {
    width: 32,
    height: 32,
    borderRadius: 16,
    backgroundColor: colors.background.primary,
    alignItems: 'center',
    justifyContent: 'center',
  },

  section: {
    marginBottom: spacing.lg,
  },

  sectionTitle: {
    fontSize: typography.fontSize.sm,
    fontWeight: typography.fontWeight.medium,
    color: colors.text.secondary,
    marginBottom: spacing.sm,
    marginHorizontal: spacing.lg,
    textTransform: 'uppercase',
  },

  sectionContent: {
    backgroundColor: colors.background.primary,
    marginHorizontal: spacing.lg,
    borderRadius: 12,
    overflow: 'hidden',
  },

  settingItem: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: spacing.md,
    paddingVertical: spacing.md,
    borderBottomWidth: 1,
    borderBottomColor: colors.border,
  },

  settingItemLeft: {
    flexDirection: 'row',
    alignItems: 'center',
    flex: 1,
  },

  settingIcon: {
    width: 36,
    height: 36,
    borderRadius: 18,
    backgroundColor: colors.primary + '15',
    alignItems: 'center',
    justifyContent: 'center',
    marginRight: spacing.md,
  },

  logoutIcon: {
    backgroundColor: colors.error + '15',
  },

  settingInfo: {
    flex: 1,
  },

  settingTitle: {
    fontSize: typography.fontSize.base,
    fontWeight: typography.fontWeight.medium,
    color: colors.text.primary,
    marginBottom: spacing.xs / 2,
  },

  logoutTitle: {
    color: colors.error,
  },

  settingSubtitle: {
    fontSize: typography.fontSize.sm,
    color: colors.text.secondary,
  },

  settingRight: {
    alignItems: 'center',
    justifyContent: 'center',
  },

  versionInfo: {
    alignItems: 'center',
    paddingVertical: spacing.xl,
  },

  versionText: {
    fontSize: typography.fontSize.sm,
    color: colors.text.secondary,
    marginBottom: spacing.xs / 2,
  },

  buildText: {
    fontSize: typography.fontSize.xs,
    color: colors.text.secondary,
  },
});

export default SettingsScreen;
