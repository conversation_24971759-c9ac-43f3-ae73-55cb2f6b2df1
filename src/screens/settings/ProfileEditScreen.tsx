import React, { useState } from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  TouchableOpacity,
  TextInput,
  KeyboardAvoidingView,
  Platform,
  Image,
  Alert,
} from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import { Ionicons } from '@expo/vector-icons';
import * as ImagePicker from 'expo-image-picker';
import { colors, spacing, typography } from '@/styles';
import { useAuthStore } from '@/stores';
import Toast from 'react-native-toast-message';

export const ProfileEditScreen: React.FC = () => {
  const { user, updateUser } = useAuthStore();
  const [isEditing, setIsEditing] = useState(false);
  const [formData, setFormData] = useState({
    username: user?.username || '',
    email: user?.email || '',
    phone: '',
    company: '',
    position: '',
    bio: '',
  });
  const [avatar, setAvatar] = useState<string | null>(null);

  const handlePickImage = async () => {
    const permissionResult =
      await ImagePicker.requestMediaLibraryPermissionsAsync();

    if (permissionResult.granted === false) {
      Alert.alert('提示', '需要相册权限才能选择头像');
      return;
    }

    const result = await ImagePicker.launchImageLibraryAsync({
      mediaTypes: ImagePicker.MediaTypeOptions.Images,
      allowsEditing: true,
      aspect: [1, 1],
      quality: 0.8,
    });

    if (!result.canceled && result.assets[0]) {
      setAvatar(result.assets[0].uri);
    }
  };

  const handleTakePhoto = async () => {
    const permissionResult = await ImagePicker.requestCameraPermissionsAsync();

    if (permissionResult.granted === false) {
      Alert.alert('提示', '需要相机权限才能拍照');
      return;
    }

    const result = await ImagePicker.launchCameraAsync({
      allowsEditing: true,
      aspect: [1, 1],
      quality: 0.8,
    });

    if (!result.canceled && result.assets[0]) {
      setAvatar(result.assets[0].uri);
    }
  };

  const showImagePicker = () => {
    Alert.alert('选择头像', '请选择获取头像的方式', [
      { text: '取消', style: 'cancel' },
      { text: '拍照', onPress: handleTakePhoto },
      { text: '从相册选择', onPress: handlePickImage },
    ]);
  };

  const handleSave = async () => {
    if (!formData.username.trim()) {
      Toast.show({
        type: 'error',
        text1: '请输入用户名',
        text2: '用户名不能为空',
      });
      return;
    }

    try {
      // 模拟保存用户信息
      await new Promise((resolve) => setTimeout(resolve, 1000));

      updateUser({
        username: formData.username,
        email: formData.email,
        avatar: avatar || undefined,
      });

      Toast.show({
        type: 'success',
        text1: '保存成功',
        text2: '个人资料已更新',
      });

      setIsEditing(false);
    } catch (error) {
      Toast.show({
        type: 'error',
        text1: '保存失败',
        text2: '请稍后重试',
      });
    }
  };

  const renderField = (
    label: string,
    value: string,
    key: keyof typeof formData,
    placeholder?: string,
    multiline = false
  ) => (
    <View style={styles.field}>
      <Text style={styles.fieldLabel}>{label}</Text>
      {isEditing ? (
        <TextInput
          style={[styles.fieldInput, multiline && styles.multilineInput]}
          value={value}
          onChangeText={(text) => setFormData({ ...formData, [key]: text })}
          placeholder={placeholder}
          placeholderTextColor={colors.text.secondary}
          editable={isEditing}
          multiline={multiline}
          numberOfLines={multiline ? 4 : 1}
        />
      ) : (
        <Text style={styles.fieldValue}>
          {value || <Text style={styles.placeholder}>未填写</Text>}
        </Text>
      )}
    </View>
  );

  return (
    <SafeAreaView style={styles.container}>
      <KeyboardAvoidingView
        style={styles.keyboardAvoid}
        behavior={Platform.OS === 'ios' ? 'padding' : 'height'}
      >
        <ScrollView
          style={styles.scrollView}
          showsVerticalScrollIndicator={false}
        >
          {/* 头部 */}
          <View style={styles.header}>
            <Text style={styles.title}>个人资料</Text>
            <TouchableOpacity
              style={styles.editButton}
              onPress={() => (isEditing ? handleSave() : setIsEditing(true))}
            >
              <Text style={styles.editButtonText}>
                {isEditing ? '保存' : '编辑'}
              </Text>
            </TouchableOpacity>
          </View>

          {/* 头像区域 */}
          <View style={styles.avatarSection}>
            <TouchableOpacity
              style={styles.avatarContainer}
              onPress={isEditing ? showImagePicker : undefined}
              disabled={!isEditing}
            >
              {avatar || user?.avatar ? (
                <Image
                  source={{ uri: avatar || user?.avatar }}
                  style={styles.avatar}
                />
              ) : (
                <View style={styles.avatarPlaceholder}>
                  <Ionicons name="person" size={48} color="#FFFFFF" />
                </View>
              )}
              {isEditing && (
                <View style={styles.avatarOverlay}>
                  <Ionicons name="camera" size={24} color="#FFFFFF" />
                </View>
              )}
            </TouchableOpacity>
            <Text style={styles.avatarHint}>
              {isEditing ? '点击更换头像' : formData.username || '未设置用户名'}
            </Text>
          </View>

          {/* 基本信息 */}
          <View style={styles.section}>
            <Text style={styles.sectionTitle}>基本信息</Text>
            {renderField(
              '用户名',
              formData.username,
              'username',
              '请输入用户名'
            )}
            {renderField('邮箱', formData.email, 'email', '请输入邮箱地址')}
            {renderField('手机号', formData.phone, 'phone', '请输入手机号')}
          </View>

          {/* 工作信息 */}
          <View style={styles.section}>
            <Text style={styles.sectionTitle}>工作信息</Text>
            {renderField('公司', formData.company, 'company', '请输入公司名称')}
            {renderField('职位', formData.position, 'position', '请输入职位')}
          </View>

          {/* 个人简介 */}
          <View style={styles.section}>
            <Text style={styles.sectionTitle}>个人简介</Text>
            {renderField('简介', formData.bio, 'bio', '介绍一下自己...', true)}
          </View>

          {/* 账户信息 */}
          <View style={styles.section}>
            <Text style={styles.sectionTitle}>账户信息</Text>
            <View style={styles.accountInfo}>
              <View style={styles.accountItem}>
                <Text style={styles.accountLabel}>用户ID</Text>
                <Text style={styles.accountValue}>{user?.id || 'user1'}</Text>
              </View>
              <View style={styles.accountItem}>
                <Text style={styles.accountLabel}>注册时间</Text>
                <Text style={styles.accountValue}>
                  {user?.createdAt
                    ? new Date(user.createdAt).toLocaleDateString('zh-CN')
                    : '2025-01-01'}
                </Text>
              </View>
            </View>
          </View>

          {/* 操作按钮 */}
          {isEditing && (
            <View style={styles.actions}>
              <TouchableOpacity
                style={[styles.actionButton, styles.cancelButton]}
                onPress={() => {
                  setIsEditing(false);
                  setFormData({
                    username: user?.username || '',
                    email: user?.email || '',
                    phone: '',
                    company: '',
                    position: '',
                    bio: '',
                  });
                  setAvatar(null);
                }}
              >
                <Text style={styles.cancelButtonText}>取消</Text>
              </TouchableOpacity>
            </View>
          )}
        </ScrollView>
      </KeyboardAvoidingView>
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: colors.background.primary,
  },
  keyboardAvoid: {
    flex: 1,
  },
  scrollView: {
    flex: 1,
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingHorizontal: spacing.lg,
    paddingVertical: spacing.md,
  },
  title: {
    fontSize: typography.fontSize.xxl,
    fontWeight: typography.fontWeight.bold as any,
    color: colors.text.primary,
  },
  editButton: {
    paddingHorizontal: spacing.md,
    paddingVertical: spacing.sm,
  },
  editButtonText: {
    fontSize: typography.fontSize.md,
    color: colors.primary,
    fontWeight: typography.fontWeight.medium as any,
  },
  avatarSection: {
    alignItems: 'center',
    paddingVertical: spacing.xl,
  },
  avatarContainer: {
    position: 'relative',
    marginBottom: spacing.md,
  },
  avatar: {
    width: 100,
    height: 100,
    borderRadius: 50,
  },
  avatarPlaceholder: {
    width: 100,
    height: 100,
    borderRadius: 50,
    backgroundColor: colors.primary,
    alignItems: 'center',
    justifyContent: 'center',
  },
  avatarOverlay: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
    borderRadius: 50,
    alignItems: 'center',
    justifyContent: 'center',
  },
  avatarHint: {
    fontSize: typography.fontSize.sm,
    color: colors.text.secondary,
  },
  section: {
    marginBottom: spacing.xl,
    paddingHorizontal: spacing.lg,
  },
  sectionTitle: {
    fontSize: typography.fontSize.lg,
    fontWeight: typography.fontWeight.semibold as any,
    color: colors.text.primary,
    marginBottom: spacing.md,
  },
  field: {
    marginBottom: spacing.md,
  },
  fieldLabel: {
    fontSize: typography.fontSize.sm,
    color: colors.text.secondary,
    marginBottom: spacing.xs,
  },
  fieldInput: {
    backgroundColor: colors.background.secondary,
    borderRadius: 8,
    padding: spacing.md,
    fontSize: typography.fontSize.md,
    color: colors.text.primary,
    borderWidth: 1,
    borderColor: colors.border,
  },
  multilineInput: {
    minHeight: 100,
    textAlignVertical: 'top',
  },
  fieldValue: {
    fontSize: typography.fontSize.md,
    color: colors.text.primary,
    paddingVertical: spacing.sm,
  },
  placeholder: {
    color: colors.text.secondary,
  },
  accountInfo: {
    backgroundColor: colors.background.secondary,
    borderRadius: 8,
    padding: spacing.md,
  },
  accountItem: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingVertical: spacing.sm,
  },
  accountLabel: {
    fontSize: typography.fontSize.sm,
    color: colors.text.secondary,
  },
  accountValue: {
    fontSize: typography.fontSize.sm,
    color: colors.text.primary,
  },
  actions: {
    paddingHorizontal: spacing.lg,
    paddingBottom: spacing.xl,
  },
  actionButton: {
    paddingVertical: spacing.md,
    borderRadius: 8,
    alignItems: 'center',
  },
  cancelButton: {
    backgroundColor: colors.background.secondary,
    borderWidth: 1,
    borderColor: colors.border,
  },
  cancelButtonText: {
    fontSize: typography.fontSize.md,
    color: colors.text.secondary,
  },
});
