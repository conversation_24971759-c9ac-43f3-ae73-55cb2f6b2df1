import React, { useState } from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  KeyboardAvoidingView,
  Platform,
  TouchableOpacity,
  Image,
  StatusBar,
  TextInput,
} from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import { useNavigation } from '@react-navigation/native';
import { StackNavigationProp } from '@react-navigation/stack';
import { LinearGradient } from 'expo-linear-gradient';
import { BlurView } from 'expo-blur';
import { Ionicons } from '@expo/vector-icons';
import { colors, spacing, typography } from '../../styles';
import { authService } from '../../services/api';
import { RootStackParamList } from '../../types/navigation';
import Toast from 'react-native-toast-message';

type RegisterScreenNavigationProp = StackNavigationProp<
  RootStackParamList,
  'Register'
>;

export const RegisterScreen: React.FC = () => {
  const navigation = useNavigation<RegisterScreenNavigationProp>();
  const [username, setUsername] = useState('');
  const [phone, setPhone] = useState('');
  const [password, setPassword] = useState('');
  const [confirmPassword, setConfirmPassword] = useState('');
  const [loading, setLoading] = useState(false);
  const [showPassword, setShowPassword] = useState(false);
  const [showConfirmPassword, setShowConfirmPassword] = useState(false);
  const [errors, setErrors] = useState({
    username: '',
    phone: '',
    password: '',
    confirmPassword: '',
  });

  // 验证用户名
  const validateUsername = (value: string) => {
    if (value.length < 4) {
      return '用户名不能少于4个字符';
    }
    return '';
  };

  // 验证手机号
  const validatePhone = (value: string) => {
    const phoneRegex = /^1[3-9]\d{9}$/;
    if (!phoneRegex.test(value)) {
      return '请输入正确的手机号';
    }
    return '';
  };

  // 验证密码
  const validatePassword = (value: string) => {
    if (value.length < 8) {
      return '密码长度不能少于8位';
    }
    if (!/[a-z]/.test(value)) {
      return '密码必须包含小写字母';
    }
    if (!/[A-Z]/.test(value)) {
      return '密码必须包含大写字母';
    }
    if (!/[!@#$%^&*(),.?":{}|<>]/.test(value)) {
      return '密码必须包含特殊字符';
    }
    return '';
  };

  // 验证确认密码
  const validateConfirmPassword = (value: string) => {
    if (value !== password) {
      return '两次输入的密码不一致';
    }
    return '';
  };

  // 处理输入变化
  const handleInputChange = (field: string, value: string) => {
    switch (field) {
      case 'username':
        setUsername(value);
        setErrors({ ...errors, username: validateUsername(value) });
        break;
      case 'phone':
        setPhone(value);
        setErrors({ ...errors, phone: validatePhone(value) });
        break;
      case 'password':
        setPassword(value);
        setErrors({ ...errors, password: validatePassword(value) });
        break;
      case 'confirmPassword':
        setConfirmPassword(value);
        setErrors({
          ...errors,
          confirmPassword: validateConfirmPassword(value),
        });
        break;
    }
  };

  // 处理注册
  const handleRegister = async () => {
    // 验证所有字段
    const usernameError = validateUsername(username);
    const phoneError = validatePhone(phone);
    const passwordError = validatePassword(password);
    const confirmPasswordError = validateConfirmPassword(confirmPassword);

    setErrors({
      username: usernameError,
      phone: phoneError,
      password: passwordError,
      confirmPassword: confirmPasswordError,
    });

    if (usernameError || phoneError || passwordError || confirmPasswordError) {
      return;
    }

    setLoading(true);
    try {
      await authService.register({
        username,
        phone,
        password,
      });

      Toast.show({
        type: 'success',
        text1: '注册成功',
        text2: '请使用您的手机号登录',
      });

      // 注册成功后跳转到登录页面，并传递手机号
      navigation.navigate('Login', { phone });
    } catch (error: any) {
      Toast.show({
        type: 'error',
        text1: '注册失败',
        text2: error.message || '请稍后重试',
      });
    } finally {
      setLoading(false);
    }
  };

  return (
    <View style={styles.container}>
      {/* 沉浸式状态栏 */}
      <StatusBar
        barStyle="light-content"
        backgroundColor="transparent"
        translucent
      />

      {/* 渐变背景 */}
      <LinearGradient
        colors={['#3C2DCE', '#3C2DCE']}
        start={{ x: 0, y: 0 }}
        end={{ x: 1, y: 0 }}
        style={styles.gradientBackground}
      />

      {/* 右侧白色滤镜覆盖 */}
      <LinearGradient
        colors={['transparent', 'rgba(255, 255, 255, 0.5)']}
        start={{ x: 0.3, y: 0 }}
        end={{ x: 1, y: 0 }}
        style={styles.whiteOverlay}
      />

      {/* 顶部区域 */}
      <SafeAreaView style={styles.topSection}>
        {/* Logo */}
        <View style={styles.titleSection}>
          <Image
            source={require('../../../assets/logo.png')}
            style={styles.logo}
            resizeMode="contain"
          />
        </View>
      </SafeAreaView>

      {/* 注册表单区域 */}
      <View style={styles.formContainer}>
        {/* 双层效果的背景层 */}
        <View style={styles.formCardShadow} />

        <BlurView intensity={20} tint="light" style={styles.formCard}>
          <KeyboardAvoidingView
            behavior={Platform.OS === 'ios' ? 'padding' : 'height'}
            style={styles.keyboardView}
          >
            <ScrollView
              contentContainerStyle={styles.scrollContent}
              showsVerticalScrollIndicator={false}
              keyboardShouldPersistTaps="handled"
            >
              <Text style={styles.formTitle}>创建账号</Text>
              <Text style={styles.formSubtitle}>请填写以下信息完成注册</Text>

              {/* 用户名输入 */}
              <View style={styles.inputContainer}>
                <Text style={styles.inputLabel}>用户名</Text>
                <TextInput
                  style={styles.textInput}
                  value={username}
                  onChangeText={(text) => handleInputChange('username', text)}
                  placeholder="请输入用户名（至少4个字符）"
                  placeholderTextColor="#9CA3AF"
                  autoCapitalize="none"
                />
                {errors.username ? (
                  <Text style={styles.errorText}>{errors.username}</Text>
                ) : null}
              </View>

              {/* 手机号输入 */}
              <View style={styles.inputContainer}>
                <Text style={styles.inputLabel}>手机号</Text>
                <TextInput
                  style={styles.textInput}
                  value={phone}
                  onChangeText={(text) => handleInputChange('phone', text)}
                  placeholder="请输入手机号"
                  placeholderTextColor="#9CA3AF"
                  keyboardType="phone-pad"
                  maxLength={11}
                />
                {errors.phone ? (
                  <Text style={styles.errorText}>{errors.phone}</Text>
                ) : null}
              </View>

              {/* 密码输入 */}
              <View style={styles.inputContainer}>
                <Text style={styles.inputLabel}>密码</Text>
                <View style={styles.passwordContainer}>
                  <TextInput
                    style={[styles.textInput, styles.passwordInput]}
                    value={password}
                    onChangeText={(text) => handleInputChange('password', text)}
                    placeholder="请输入密码"
                    placeholderTextColor="#9CA3AF"
                    secureTextEntry={!showPassword}
                  />
                  <TouchableOpacity
                    style={styles.eyeButton}
                    onPress={() => setShowPassword(!showPassword)}
                  >
                    <Ionicons
                      name={showPassword ? 'eye-off' : 'eye'}
                      size={20}
                      color="#9CA3AF"
                    />
                  </TouchableOpacity>
                </View>
                <Text style={styles.passwordHint}>
                  密码需包含大小写字母、特殊字符，且不少于8位
                </Text>
                {errors.password ? (
                  <Text style={styles.errorText}>{errors.password}</Text>
                ) : null}
              </View>

              {/* 确认密码输入 */}
              <View style={styles.inputContainer}>
                <Text style={styles.inputLabel}>确认密码</Text>
                <View style={styles.passwordContainer}>
                  <TextInput
                    style={[styles.textInput, styles.passwordInput]}
                    value={confirmPassword}
                    onChangeText={(text) =>
                      handleInputChange('confirmPassword', text)
                    }
                    placeholder="请再次输入密码"
                    placeholderTextColor="#9CA3AF"
                    secureTextEntry={!showConfirmPassword}
                  />
                  <TouchableOpacity
                    style={styles.eyeButton}
                    onPress={() => setShowConfirmPassword(!showConfirmPassword)}
                  >
                    <Ionicons
                      name={showConfirmPassword ? 'eye-off' : 'eye'}
                      size={20}
                      color="#9CA3AF"
                    />
                  </TouchableOpacity>
                </View>
                {errors.confirmPassword ? (
                  <Text style={styles.errorText}>{errors.confirmPassword}</Text>
                ) : null}
              </View>

              {/* 注册按钮 */}
              <TouchableOpacity
                style={styles.registerButton}
                onPress={handleRegister}
                disabled={loading}
              >
                <LinearGradient
                  colors={['#3D37D2', '#E4A6FA']}
                  start={{ x: 0, y: 0 }}
                  end={{ x: 1, y: 0 }}
                  style={styles.registerGradient}
                >
                  <Text style={styles.registerText}>
                    {loading ? '注册中...' : '注册'}
                  </Text>
                </LinearGradient>
              </TouchableOpacity>

              {/* 返回登录 */}
              <View style={styles.footer}>
                <Text style={styles.footerText}>已有账号？</Text>
                <TouchableOpacity onPress={() => navigation.goBack()}>
                  <Text style={styles.loginLink}>立即登录</Text>
                </TouchableOpacity>
              </View>
            </ScrollView>
          </KeyboardAvoidingView>
        </BlurView>
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#3C2DCE',
  },
  gradientBackground: {
    position: 'absolute',
    left: 0,
    right: 0,
    top: 0,
    bottom: 0,
  },
  whiteOverlay: {
    position: 'absolute',
    left: 0,
    right: 0,
    top: 0,
    bottom: 0,
  },
  keyboardView: {
    flex: 1,
  },
  topSection: {
    flex: 0.25,
    paddingTop: Platform.OS === 'android' ? StatusBar.currentHeight : 0,
    minHeight: 180,
  },
  titleSection: {
    flex: 1,
    alignItems: 'center',
    justifyContent: 'center',
  },
  logo: {
    width: 80,
    height: 80,
    marginBottom: spacing.md,
  },
  appTitle: {
    fontSize: 48,
    fontWeight: 'bold',
    color: 'white',
    letterSpacing: -1,
  },
  formContainer: {
    flex: 0.75,
    position: 'relative',
    minHeight: 500,
  },
  formCardShadow: {
    position: 'absolute',
    left: 0,
    right: 0,
    top: -10,
    bottom: 0,
    backgroundColor: 'rgba(255, 255, 255, 0.5)',
    borderTopLeftRadius: 32,
    borderTopRightRadius: 32,
  },
  formCard: {
    flex: 1,
    borderTopLeftRadius: 32,
    borderTopRightRadius: 32,
    overflow: 'hidden',
    backgroundColor: 'rgba(255, 255, 255, 0.95)',
  },
  scrollContent: {
    flexGrow: 1,
    padding: spacing.lg,
    paddingTop: spacing.xl,
    paddingBottom: spacing.lg,
  },
  formTitle: {
    fontSize: 28,
    fontWeight: 'bold',
    color: '#1F2937',
    textAlign: 'center',
    marginBottom: spacing.xs,
  },
  formSubtitle: {
    fontSize: 14,
    color: '#6B7280',
    textAlign: 'center',
    marginBottom: spacing.lg,
  },
  inputContainer: {
    marginBottom: spacing.md,
  },
  inputLabel: {
    fontSize: 14,
    color: '#6B7280',
    marginBottom: spacing.sm,
    fontWeight: '500',
  },
  textInput: {
    borderWidth: 1,
    borderColor: '#E5E7EB',
    borderRadius: 12,
    paddingHorizontal: spacing.md,
    paddingVertical: spacing.md,
    fontSize: 16,
    color: '#1F2937',
    backgroundColor: '#F9FAFB',
  },
  passwordContainer: {
    position: 'relative',
  },
  passwordInput: {
    paddingRight: 50,
  },
  eyeButton: {
    position: 'absolute',
    right: spacing.md,
    top: spacing.md,
    padding: spacing.xs,
  },
  passwordHint: {
    fontSize: 11,
    color: '#6B7280',
    marginTop: spacing.xs,
  },
  errorText: {
    fontSize: 11,
    color: colors.error,
    marginTop: spacing.xs,
  },
  registerButton: {
    marginTop: spacing.md,
    marginBottom: spacing.md,
    borderRadius: 12,
    overflow: 'hidden',
  },
  registerGradient: {
    paddingVertical: spacing.sm,
    alignItems: 'center',
    justifyContent: 'center',
  },
  registerText: {
    color: 'white',
    fontSize: 18,
    fontWeight: '600',
  },
  footer: {
    flexDirection: 'row',
    justifyContent: 'center',
    alignItems: 'center',
    marginBottom: spacing.sm,
  },
  footerText: {
    fontSize: typography.sizes.sm,
    color: colors.gray[500],
  },
  loginLink: {
    fontSize: 14,
    color: '#3D37D2',
    fontWeight: '600',
    marginLeft: spacing.xs,
  },
});
