import React, { useState } from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  TouchableOpacity,
  TextInput,
  KeyboardAvoidingView,
  Platform,
  ActivityIndicator,
  Modal,
} from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import { Ionicons } from '@expo/vector-icons';
import { colors, spacing, typography } from '@/styles';
import { useWritingStore } from '@/stores';
import { WritingContent } from '@/types';
import Toast from 'react-native-toast-message';

export const WritingScreen: React.FC = () => {
  const [activeTab, setActiveTab] = useState<'create' | 'optimize'>('create');
  const [inputText, setInputText] = useState('');
  const [isGenerating, setIsGenerating] = useState(false);
  const [showResult, setShowResult] = useState(false);
  const [generatedContent, setGeneratedContent] = useState('');
  const [selectedTemplate, setSelectedTemplate] = useState<string | null>(null);
  const [wordCount, setWordCount] = useState(1000);

  const { contents, addContent } = useWritingStore();

  // 写作模板
  const templates = [
    {
      id: 'xiaohongshu',
      name: '小红书文案',
      icon: 'heart',
      color: colors.error,
    },
    { id: 'product', name: '产品介绍', icon: 'cube', color: colors.primary },
    { id: 'email', name: '邮件回复', icon: 'mail', color: colors.info },
    {
      id: 'report',
      name: '工作报告',
      icon: 'document-text',
      color: colors.success,
    },
    { id: 'meeting', name: '会议纪要', icon: 'people', color: colors.warning },
    {
      id: 'article',
      name: '文章创作',
      icon: 'create',
      color: colors.secondary,
    },
  ];

  // 字数选项
  const wordCountOptions = [100, 500, 1000, 2000, 3000];

  const handleGenerate = async () => {
    if (!inputText.trim()) {
      Toast.show({
        type: 'error',
        text1: '请输入内容',
        text2: '请输入您的写作需求或要优化的文案',
      });
      return;
    }

    setIsGenerating(true);
    try {
      // 模拟AI生成
      await new Promise((resolve) => setTimeout(resolve, 2000));

      const mockContent =
        activeTab === 'create'
          ? `根据您的需求"${inputText}"，我为您生成了以下内容：\n\n【标题】${inputText}的深度解析\n\n【正文】\n在当今快速发展的时代，${inputText}已经成为了一个备受关注的话题。本文将从多个角度深入探讨这一主题，为您提供全面的见解和实用的建议。\n\n首先，让我们了解一下${inputText}的背景和重要性...\n\n【结论】\n通过以上分析，我们可以看到${inputText}在现代社会中扮演着重要角色。希望本文能够为您提供有价值的参考。`
          : `您的文案已优化：\n\n${inputText}\n\n【优化建议】\n1. 语言更加精炼流畅\n2. 结构更加清晰有序\n3. 表达更加生动有力`;

      setGeneratedContent(mockContent);
      setShowResult(true);

      // 保存到历史记录
      const newContent: WritingContent = {
        id: Date.now().toString(),
        title: inputText.substring(0, 50),
        content: mockContent,
        prompt: inputText,
        type: activeTab === 'create' ? 'article' : 'optimization',
        wordCount: mockContent.length,
        createdAt: new Date(),
        updatedAt: new Date(),
        userId: 'user1',
      };
      addContent(newContent);
    } catch (error) {
      Toast.show({
        type: 'error',
        text1: '生成失败',
        text2: '请检查网络连接后重试',
      });
    } finally {
      setIsGenerating(false);
    }
  };

  const handleTemplateSelect = (templateId: string) => {
    setSelectedTemplate(templateId);
    // 根据模板设置默认提示词
    const templatePrompts: Record<string, string> = {
      xiaohongshu: '请帮我写一篇关于[主题]的小红书笔记，要求真实、有趣、实用',
      product: '请为[产品名称]写一份吸引人的产品介绍',
      email: '请帮我回复这封邮件：[邮件内容]',
      report: '请帮我写一份关于[项目/工作]的总结报告',
      meeting: '请根据以下要点生成会议纪要：[会议要点]',
      article: '请写一篇关于[主题]的深度文章',
    };
    setInputText(templatePrompts[templateId] || '');
  };

  const renderTemplate = (template: (typeof templates)[0]) => (
    <TouchableOpacity
      key={template.id}
      style={[
        styles.templateCard,
        selectedTemplate === template.id && styles.templateCardActive,
      ]}
      onPress={() => handleTemplateSelect(template.id)}
    >
      <View
        style={[
          styles.templateIcon,
          { backgroundColor: template.color + '20' },
        ]}
      >
        <Ionicons
          name={template.icon as any}
          size={24}
          color={template.color}
        />
      </View>
      <Text style={styles.templateName}>{template.name}</Text>
    </TouchableOpacity>
  );

  const renderWordCountOption = (count: number) => (
    <TouchableOpacity
      key={count}
      style={[
        styles.wordCountOption,
        wordCount === count && styles.wordCountOptionActive,
      ]}
      onPress={() => setWordCount(count)}
    >
      <Text
        style={[
          styles.wordCountText,
          wordCount === count && styles.wordCountTextActive,
        ]}
      >
        {count}字
      </Text>
    </TouchableOpacity>
  );

  const renderRecentItem = (item: WritingContent) => (
    <TouchableOpacity key={item.id} style={styles.recentItem}>
      <View style={styles.recentItemContent}>
        <Text style={styles.recentItemTitle} numberOfLines={1}>
          {item.title}
        </Text>
        <Text style={styles.recentItemMeta}>
          {item.wordCount}字 •{' '}
          {new Date(item.createdAt).toLocaleDateString('zh-CN')}
        </Text>
      </View>
      <Ionicons
        name="chevron-forward"
        size={20}
        color={colors.text.secondary}
      />
    </TouchableOpacity>
  );

  return (
    <SafeAreaView style={styles.container}>
      <KeyboardAvoidingView
        style={styles.keyboardAvoid}
        behavior={Platform.OS === 'ios' ? 'padding' : 'height'}
      >
        <ScrollView
          style={styles.scrollView}
          showsVerticalScrollIndicator={false}
        >
          {/* 功能切换标签 */}
          <View style={styles.tabs}>
            <TouchableOpacity
              style={[styles.tab, activeTab === 'create' && styles.tabActive]}
              onPress={() => setActiveTab('create')}
            >
              <Text
                style={[
                  styles.tabText,
                  activeTab === 'create' && styles.tabTextActive,
                ]}
              >
                开始创作
              </Text>
            </TouchableOpacity>
            <TouchableOpacity
              style={[styles.tab, activeTab === 'optimize' && styles.tabActive]}
              onPress={() => setActiveTab('optimize')}
            >
              <Text
                style={[
                  styles.tabText,
                  activeTab === 'optimize' && styles.tabTextActive,
                ]}
              >
                文案优化
              </Text>
            </TouchableOpacity>
          </View>

          {/* 写作模板（仅在创作模式显示） */}
          {activeTab === 'create' && (
            <View style={styles.section}>
              <Text style={styles.sectionTitle}>选择模板</Text>
              <View style={styles.templates}>
                {templates.map(renderTemplate)}
              </View>
            </View>
          )}

          {/* 输入区域 */}
          <View style={styles.section}>
            <Text style={styles.sectionTitle}>
              {activeTab === 'create' ? '描述您的需求' : '输入要优化的文案'}
            </Text>
            <View style={styles.inputContainer}>
              <TextInput
                style={styles.input}
                placeholder={
                  activeTab === 'create'
                    ? '例如：写一篇关于健康饮食的文章，包含营养搭配建议...'
                    : '粘贴您要优化的文案内容...'
                }
                placeholderTextColor={colors.text.secondary}
                value={inputText}
                onChangeText={setInputText}
                multiline
                textAlignVertical="top"
              />
              <TouchableOpacity style={styles.voiceButton}>
                <Ionicons name="mic" size={24} color={colors.primary} />
              </TouchableOpacity>
            </View>
            <Text style={styles.charCount}>{inputText.length} 字</Text>
          </View>

          {/* 字数选择（仅在创作模式显示） */}
          {activeTab === 'create' && (
            <View style={styles.section}>
              <Text style={styles.sectionTitle}>生成字数</Text>
              <View style={styles.wordCountOptions}>
                {wordCountOptions.map(renderWordCountOption)}
              </View>
            </View>
          )}

          {/* 生成按钮 */}
          <TouchableOpacity
            style={[
              styles.generateButton,
              isGenerating && styles.generateButtonDisabled,
            ]}
            onPress={handleGenerate}
            disabled={isGenerating}
          >
            {isGenerating ? (
              <ActivityIndicator color="#FFFFFF" />
            ) : (
              <>
                <Ionicons name="sparkles" size={20} color="#FFFFFF" />
                <Text style={styles.generateButtonText}>
                  {activeTab === 'create' ? '开始生成' : '优化文案'}
                </Text>
              </>
            )}
          </TouchableOpacity>

          {/* 最近使用 */}
          <View style={styles.section}>
            <View style={styles.sectionHeader}>
              <Text style={styles.sectionTitle}>最近使用</Text>
              <TouchableOpacity>
                <Text style={styles.sectionAction}>查看全部</Text>
              </TouchableOpacity>
            </View>
            <View style={styles.recentList}>
              {contents.slice(0, 3).map(renderRecentItem)}
              {contents.length === 0 && (
                <Text style={styles.emptyText}>暂无历史记录</Text>
              )}
            </View>
          </View>
        </ScrollView>
      </KeyboardAvoidingView>

      {/* 结果展示模态框 */}
      <Modal
        visible={showResult}
        animationType="slide"
        transparent={true}
        onRequestClose={() => setShowResult(false)}
      >
        <View style={styles.modalOverlay}>
          <View style={styles.modalContent}>
            <View style={styles.modalHeader}>
              <Text style={styles.modalTitle}>生成结果</Text>
              <TouchableOpacity onPress={() => setShowResult(false)}>
                <Ionicons name="close" size={24} color={colors.text.primary} />
              </TouchableOpacity>
            </View>
            <ScrollView style={styles.modalBody}>
              <Text style={styles.generatedText}>{generatedContent}</Text>
            </ScrollView>
            <View style={styles.modalActions}>
              <TouchableOpacity style={styles.actionButton}>
                <Ionicons
                  name="copy-outline"
                  size={20}
                  color={colors.primary}
                />
                <Text style={styles.actionButtonText}>复制</Text>
              </TouchableOpacity>
              <TouchableOpacity style={styles.actionButton}>
                <Ionicons
                  name="create-outline"
                  size={20}
                  color={colors.primary}
                />
                <Text style={styles.actionButtonText}>编辑</Text>
              </TouchableOpacity>
              <TouchableOpacity style={styles.actionButton}>
                <Ionicons
                  name="chatbubble-outline"
                  size={20}
                  color={colors.primary}
                />
                <Text style={styles.actionButtonText}>继续对话</Text>
              </TouchableOpacity>
            </View>
          </View>
        </View>
      </Modal>
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: colors.background.primary,
  },
  keyboardAvoid: {
    flex: 1,
  },
  scrollView: {
    flex: 1,
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingHorizontal: spacing.lg,
    paddingVertical: spacing.md,
  },
  title: {
    fontSize: typography.fontSize.xxl,
    fontWeight: typography.fontWeight.bold as any,
    color: colors.text.primary,
  },
  historyButton: {
    padding: spacing.sm,
  },
  tabs: {
    flexDirection: 'row',
    marginHorizontal: spacing.lg,
    backgroundColor: colors.background.secondary,
    borderRadius: 12,
    padding: spacing.xs,
    marginBottom: spacing.lg,
  },
  tab: {
    flex: 1,
    paddingVertical: spacing.sm,
    alignItems: 'center',
    borderRadius: 8,
  },
  tabActive: {
    backgroundColor: colors.background.primary,
  },
  tabText: {
    fontSize: typography.fontSize.md,
    color: colors.text.secondary,
  },
  tabTextActive: {
    color: colors.text.primary,
    fontWeight: typography.fontWeight.medium as any,
  },
  section: {
    marginBottom: spacing.xl,
    paddingHorizontal: spacing.lg,
  },
  sectionHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: spacing.md,
  },
  sectionTitle: {
    fontSize: typography.fontSize.lg,
    fontWeight: typography.fontWeight.semibold as any,
    color: colors.text.primary,
    marginBottom: spacing.md,
  },
  sectionAction: {
    fontSize: typography.fontSize.sm,
    color: colors.primary,
  },
  templates: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    gap: spacing.sm,
  },
  templateCard: {
    width: '31%',
    alignItems: 'center',
    padding: spacing.md,
    backgroundColor: colors.background.secondary,
    borderRadius: 12,
    borderWidth: 2,
    borderColor: 'transparent',
  },
  templateCardActive: {
    borderColor: colors.primary,
    backgroundColor: colors.primary + '10',
  },
  templateIcon: {
    width: 48,
    height: 48,
    borderRadius: 24,
    justifyContent: 'center',
    alignItems: 'center',
    marginBottom: spacing.sm,
  },
  templateName: {
    fontSize: typography.fontSize.sm,
    color: colors.text.primary,
    textAlign: 'center',
  },
  inputContainer: {
    backgroundColor: colors.background.secondary,
    borderRadius: 12,
    padding: spacing.md,
    minHeight: 150,
  },
  input: {
    fontSize: typography.fontSize.md,
    color: colors.text.primary,
    lineHeight: 24,
    minHeight: 120,
  },
  voiceButton: {
    position: 'absolute',
    bottom: spacing.md,
    right: spacing.md,
    width: 40,
    height: 40,
    borderRadius: 20,
    backgroundColor: colors.background.primary,
    justifyContent: 'center',
    alignItems: 'center',
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },
  charCount: {
    fontSize: typography.fontSize.sm,
    color: colors.text.secondary,
    marginTop: spacing.xs,
    textAlign: 'right',
  },
  wordCountOptions: {
    flexDirection: 'row',
    gap: spacing.sm,
  },
  wordCountOption: {
    paddingHorizontal: spacing.lg,
    paddingVertical: spacing.sm,
    backgroundColor: colors.background.secondary,
    borderRadius: 20,
    borderWidth: 1,
    borderColor: colors.border,
  },
  wordCountOptionActive: {
    backgroundColor: colors.primary,
    borderColor: colors.primary,
  },
  wordCountText: {
    fontSize: typography.fontSize.sm,
    color: colors.text.primary,
  },
  wordCountTextActive: {
    color: '#FFFFFF',
  },
  generateButton: {
    flexDirection: 'row',
    backgroundColor: colors.primary,
    marginHorizontal: spacing.lg,
    paddingVertical: spacing.md,
    borderRadius: 12,
    alignItems: 'center',
    justifyContent: 'center',
    gap: spacing.sm,
    marginBottom: spacing.xl,
  },
  generateButtonDisabled: {
    opacity: 0.6,
  },
  generateButtonText: {
    fontSize: typography.fontSize.md,
    fontWeight: typography.fontWeight.semibold as any,
    color: '#FFFFFF',
  },
  recentList: {
    gap: spacing.sm,
  },
  recentItem: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: colors.background.secondary,
    padding: spacing.md,
    borderRadius: 12,
    marginBottom: spacing.sm,
  },
  recentItemContent: {
    flex: 1,
  },
  recentItemTitle: {
    fontSize: typography.fontSize.md,
    color: colors.text.primary,
    marginBottom: spacing.xs,
  },
  recentItemMeta: {
    fontSize: typography.fontSize.xs,
    color: colors.text.secondary,
  },
  emptyText: {
    textAlign: 'center',
    color: colors.text.secondary,
    fontSize: typography.fontSize.sm,
    paddingVertical: spacing.xl,
  },
  modalOverlay: {
    flex: 1,
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
    justifyContent: 'flex-end',
  },
  modalContent: {
    backgroundColor: colors.background.primary,
    borderTopLeftRadius: 20,
    borderTopRightRadius: 20,
    maxHeight: '90%',
  },
  modalHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    padding: spacing.lg,
    borderBottomWidth: 1,
    borderBottomColor: colors.border,
  },
  modalTitle: {
    fontSize: typography.fontSize.lg,
    fontWeight: typography.fontWeight.semibold as any,
    color: colors.text.primary,
  },
  modalBody: {
    padding: spacing.lg,
    maxHeight: 400,
  },
  generatedText: {
    fontSize: typography.fontSize.md,
    color: colors.text.primary,
    lineHeight: 24,
  },
  modalActions: {
    flexDirection: 'row',
    padding: spacing.lg,
    borderTopWidth: 1,
    borderTopColor: colors.border,
    gap: spacing.md,
  },
  actionButton: {
    flex: 1,
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    gap: spacing.xs,
    paddingVertical: spacing.sm,
    borderRadius: 8,
    backgroundColor: colors.background.secondary,
  },
  actionButtonText: {
    fontSize: typography.fontSize.sm,
    color: colors.primary,
  },
});
