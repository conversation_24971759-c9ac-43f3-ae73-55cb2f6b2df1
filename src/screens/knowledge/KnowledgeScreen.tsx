import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  TouchableOpacity,
  RefreshControl,
  Alert,
  ActivityIndicator,
} from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import { Ionicons } from '@expo/vector-icons';
import * as DocumentPicker from 'expo-document-picker';
import { colors, spacing, typography } from '@/styles';
import { useKnowledgeStore } from '@/stores';
import { KnowledgeDocument, KnowledgeCategory } from '@/types';
import Toast from 'react-native-toast-message';
import { useNavigation } from '@react-navigation/native';

export const KnowledgeScreen: React.FC = () => {
  const navigation = useNavigation();
  const [refreshing, setRefreshing] = useState(false);
  const [isLoading, setIsLoading] = useState(false);
  const { documents, categories, addDocument, setCategories } =
    useKnowledgeStore();

  // 统计信息
  const stats = {
    totalDocuments: documents.length,
    audioCount: documents.filter((doc) => doc.type === 'audio').length,
    documentCount: documents.filter((doc) =>
      ['pdf', 'word', 'text'].includes(doc.type)
    ).length,
    totalSize: documents.reduce((sum, doc) => sum + doc.size, 0),
  };

  useEffect(() => {
    loadInitialData();
  }, []);

  const loadInitialData = async () => {
    setIsLoading(true);
    try {
      // 模拟加载数据
      await new Promise((resolve) => setTimeout(resolve, 1000));

      // 加载示例分类
      const sampleCategories: KnowledgeCategory[] = [
        {
          id: '1',
          name: '工作文档',
          description: '工作相关的文档资料',
          order: 1,
          createdAt: new Date(),
          updatedAt: new Date(),
          userId: 'user1',
        },
        {
          id: '2',
          name: '会议录音',
          description: '会议录音和转写文件',
          order: 2,
          createdAt: new Date(),
          updatedAt: new Date(),
          userId: 'user1',
        },
      ];
      setCategories(sampleCategories);
    } catch (error) {
      Toast.show({
        type: 'error',
        text1: '加载失败',
        text2: '无法加载知识库数据',
      });
    } finally {
      setIsLoading(false);
    }
  };

  const onRefresh = async () => {
    setRefreshing(true);
    await loadInitialData();
    setRefreshing(false);
  };

  const handleUploadDocument = async () => {
    try {
      const result = await DocumentPicker.getDocumentAsync({
        type: [
          'application/pdf',
          'application/msword',
          'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
          'text/plain',
        ],
        copyToCacheDirectory: true,
      });

      if (!result.canceled && result.assets[0]) {
        const asset = result.assets[0];

        const newDocument: KnowledgeDocument = {
          id: Date.now().toString(),
          title: asset.name,
          content: '', // 实际应用中需要解析文档内容
          type: getDocumentType(asset.mimeType || ''),
          size: asset.size || 0,
          filePath: asset.uri,
          tags: [],
          createdAt: new Date(),
          updatedAt: new Date(),
          userId: 'user1',
        };

        addDocument(newDocument);
        Toast.show({
          type: 'success',
          text1: '上传成功',
          text2: `已添加文档: ${asset.name}`,
        });
      }
    } catch (error) {
      Toast.show({
        type: 'error',
        text1: '上传失败',
        text2: '无法上传文档',
      });
    }
  };

  const handleUploadAudio = async () => {
    try {
      const result = await DocumentPicker.getDocumentAsync({
        type: 'audio/*',
        copyToCacheDirectory: true,
      });

      if (!result.canceled && result.assets[0]) {
        const asset = result.assets[0];

        const newDocument: KnowledgeDocument = {
          id: Date.now().toString(),
          title: asset.name,
          content: '', // 音频文件的转写内容
          type: 'audio',
          size: asset.size || 0,
          filePath: asset.uri,
          tags: [],
          createdAt: new Date(),
          updatedAt: new Date(),
          userId: 'user1',
        };

        addDocument(newDocument);
        Toast.show({
          type: 'success',
          text1: '上传成功',
          text2: `已添加音频: ${asset.name}`,
        });
      }
    } catch (error) {
      Toast.show({
        type: 'error',
        text1: '上传失败',
        text2: '无法上传音频文件',
      });
    }
  };

  const getDocumentType = (mimeType: string): KnowledgeDocument['type'] => {
    if (mimeType.includes('pdf')) return 'pdf';
    if (mimeType.includes('word') || mimeType.includes('document'))
      return 'word';
    if (mimeType.includes('text')) return 'text';
    if (mimeType.includes('audio')) return 'audio';
    return 'other';
  };

  const formatFileSize = (bytes: number): string => {
    if (bytes === 0) return '0 B';
    const k = 1024;
    const sizes = ['B', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  };

  const renderStatCard = (
    title: string,
    value: string | number,
    icon: string,
    color: string
  ) => (
    <View style={[styles.statCard, { backgroundColor: color + '20' }]}>
      <Ionicons name={icon as any} size={24} color={color} />
      <Text style={styles.statValue}>{value}</Text>
      <Text style={styles.statTitle}>{title}</Text>
    </View>
  );

  const renderQuickAction = (
    title: string,
    icon: string,
    onPress: () => void,
    color: string = colors.primary
  ) => (
    <TouchableOpacity style={styles.quickAction} onPress={onPress}>
      <View style={[styles.quickActionIcon, { backgroundColor: color + '20' }]}>
        <Ionicons name={icon as any} size={24} color={color} />
      </View>
      <Text style={styles.quickActionText}>{title}</Text>
    </TouchableOpacity>
  );

  const renderCategory = (category: KnowledgeCategory) => {
    const categoryDocs = documents.filter(
      (doc) => doc.categoryId === category.id
    );
    return (
      <TouchableOpacity key={category.id} style={styles.categoryCard}>
        <View style={styles.categoryHeader}>
          <Ionicons name="folder" size={24} color={colors.primary} />
          <Text style={styles.categoryName}>{category.name}</Text>
          <Text style={styles.categoryCount}>{categoryDocs.length}</Text>
        </View>
        {category.description && (
          <Text style={styles.categoryDescription}>{category.description}</Text>
        )}
      </TouchableOpacity>
    );
  };

  const renderRecentDocument = (doc: KnowledgeDocument) => {
    const getDocIcon = () => {
      switch (doc.type) {
        case 'pdf':
          return 'document-text';
        case 'word':
          return 'document';
        case 'audio':
          return 'musical-notes';
        default:
          return 'document-outline';
      }
    };

    return (
      <TouchableOpacity 
        key={doc.id} 
        style={styles.recentItem}
        onPress={() => navigation.navigate('DocumentReader' as any, { documentId: doc.id })}
      >
        <Ionicons
          name={getDocIcon() as any}
          size={20}
          color={colors.text.secondary}
        />
        <View style={styles.recentItemContent}>
          <Text style={styles.recentItemTitle} numberOfLines={1}>
            {doc.title}
          </Text>
          <Text style={styles.recentItemMeta}>
            {formatFileSize(doc.size)} •{' '}
            {new Date(doc.createdAt).toLocaleDateString('zh-CN')}
          </Text>
        </View>
      </TouchableOpacity>
    );
  };

  if (isLoading) {
    return (
      <SafeAreaView style={styles.container}>
        <View style={styles.loadingContainer}>
          <ActivityIndicator size="large" color={colors.primary} />
          <Text style={styles.loadingText}>加载知识库...</Text>
        </View>
      </SafeAreaView>
    );
  }

  return (
    <SafeAreaView style={styles.container}>
      <ScrollView
        style={styles.scrollView}
        refreshControl={
          <RefreshControl refreshing={refreshing} onRefresh={onRefresh} />
        }
      >
        {/* 统计信息 */}
        <View style={styles.statsContainer}>
          {renderStatCard(
            '总文件',
            stats.totalDocuments,
            'folder-open',
            colors.primary
          )}
          {renderStatCard(
            '文档',
            stats.documentCount,
            'document-text',
            colors.success
          )}
          {renderStatCard(
            '音频',
            stats.audioCount,
            'musical-notes',
            colors.warning
          )}
          {renderStatCard(
            '存储',
            formatFileSize(stats.totalSize),
            'cloud',
            colors.info
          )}
        </View>

        {/* 搜索栏 */}
        <TouchableOpacity 
          style={styles.searchBar}
          onPress={() => navigation.navigate('Search' as any)}
        >
          <Ionicons name="search" size={20} color={colors.text.secondary} />
          <Text style={styles.searchPlaceholder}>搜索文档、音频、标签...</Text>
          <Ionicons name="funnel" size={20} color={colors.text.secondary} />
        </TouchableOpacity>

        {/* 快速操作 */}
        <View style={styles.section}>
          <View style={styles.sectionHeader}>
            <Text style={styles.sectionTitle}>快速操作</Text>
          </View>
          <View style={styles.quickActions}>
            {renderQuickAction(
              '文件上传',
              'cloud-upload',
              () => navigation.navigate('FileUpload' as any)
            )}
            {renderQuickAction(
              '文件管理',
              'folder-open',
              () => navigation.navigate('FileManagement' as any),
              colors.warning
            )}
            {renderQuickAction(
              '新建分类',
              'add-circle',
              () => navigation.navigate('CategoryManagement' as any),
              colors.success
            )}
            {renderQuickAction(
              'AI对话',
              'chatbubbles',
              () => {
                Alert.alert('提示', 'AI对话功能开发中');
              },
              colors.info
            )}
            {renderQuickAction(
              '创建文档',
              'create',
              () => navigation.navigate('DocumentEditor' as any),
              colors.success
            )}
          </View>
        </View>

        {/* 分类概览 */}
        <View style={styles.section}>
          <View style={styles.sectionHeader}>
            <Text style={styles.sectionTitle}>分类</Text>
            <TouchableOpacity onPress={() => navigation.navigate('CategoryManagement' as any)}>
              <Text style={styles.sectionAction}>管理</Text>
            </TouchableOpacity>
          </View>
          <View style={styles.categories}>
            {categories.map(renderCategory)}
            <TouchableOpacity style={[styles.categoryCard, styles.addCategory]}>
              <Ionicons
                name="add-circle-outline"
                size={32}
                color={colors.text.secondary}
              />
              <Text style={styles.addCategoryText}>添加分类</Text>
            </TouchableOpacity>
          </View>
        </View>

        {/* 最近文档 */}
        <View style={styles.section}>
          <View style={styles.sectionHeader}>
            <Text style={styles.sectionTitle}>最近文档</Text>
            <TouchableOpacity onPress={() => navigation.navigate('FileManagement' as any)}>
              <Text style={styles.sectionAction}>查看全部</Text>
            </TouchableOpacity>
          </View>
          <View style={styles.recentList}>
            {documents.slice(0, 5).map(renderRecentDocument)}
            {documents.length === 0 && (
              <Text style={styles.emptyText}>暂无文档</Text>
            )}
          </View>
        </View>
      </ScrollView>
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: colors.background.primary,
  },
  scrollView: {
    flex: 1,
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  loadingText: {
    marginTop: spacing.md,
    fontSize: typography.fontSize.md,
    color: colors.text.secondary,
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingHorizontal: spacing.lg,
    paddingVertical: spacing.md,
  },
  title: {
    fontSize: typography.fontSize.xxl,
    fontWeight: typography.fontWeight.bold as any,
    color: colors.text.primary,
  },
  searchButton: {
    padding: spacing.sm,
  },
  statsContainer: {
    flexDirection: 'row',
    paddingHorizontal: spacing.lg,
    marginBottom: spacing.md,
    marginTop: spacing.sm,
    gap: spacing.sm,
  },
  statCard: {
    flex: 1,
    padding: spacing.md,
    borderRadius: 12,
    alignItems: 'center',
  },
  statValue: {
    fontSize: typography.fontSize.lg,
    fontWeight: typography.fontWeight.semibold as any,
    color: colors.text.primary,
    marginTop: spacing.xs,
  },
  statTitle: {
    fontSize: typography.fontSize.xs,
    color: colors.text.secondary,
    marginTop: spacing.xs,
  },
  section: {
    marginBottom: spacing.lg,
  },
  sectionHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingHorizontal: spacing.lg,
    marginBottom: spacing.md,
  },
  sectionTitle: {
    fontSize: typography.fontSize.lg,
    fontWeight: typography.fontWeight.semibold as any,
    color: colors.text.primary,
  },
  sectionAction: {
    fontSize: typography.fontSize.sm,
    color: colors.primary,
  },
  quickActions: {
    flexDirection: 'row',
    paddingHorizontal: spacing.lg,
    gap: spacing.sm,
  },
  quickAction: {
    flex: 1,
    alignItems: 'center',
  },
  quickActionIcon: {
    width: 56,
    height: 56,
    borderRadius: 28,
    justifyContent: 'center',
    alignItems: 'center',
    marginBottom: spacing.xs,
  },
  quickActionText: {
    fontSize: typography.fontSize.xs,
    color: colors.text.secondary,
    textAlign: 'center',
  },
  categories: {
    paddingHorizontal: spacing.lg,
    gap: spacing.sm,
  },
  categoryCard: {
    backgroundColor: colors.background.secondary,
    padding: spacing.md,
    borderRadius: 12,
    marginBottom: spacing.sm,
  },
  categoryHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: spacing.sm,
  },
  categoryName: {
    flex: 1,
    fontSize: typography.fontSize.md,
    fontWeight: typography.fontWeight.medium as any,
    color: colors.text.primary,
  },
  categoryCount: {
    fontSize: typography.fontSize.sm,
    color: colors.text.secondary,
    backgroundColor: colors.background.tertiary,
    paddingHorizontal: spacing.sm,
    paddingVertical: spacing.xs,
    borderRadius: 12,
  },
  categoryDescription: {
    fontSize: typography.fontSize.sm,
    color: colors.text.secondary,
    marginTop: spacing.xs,
    marginLeft: 32,
  },
  addCategory: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    gap: spacing.sm,
    borderStyle: 'dashed',
    borderWidth: 1,
    borderColor: colors.border,
  },
  addCategoryText: {
    fontSize: typography.fontSize.sm,
    color: colors.text.secondary,
  },
  recentList: {
    paddingHorizontal: spacing.lg,
  },
  recentItem: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: colors.background.secondary,
    padding: spacing.md,
    borderRadius: 12,
    marginBottom: spacing.sm,
    gap: spacing.sm,
  },
  recentItemContent: {
    flex: 1,
  },
  recentItemTitle: {
    fontSize: typography.fontSize.md,
    color: colors.text.primary,
    marginBottom: spacing.xs,
  },
  recentItemMeta: {
    fontSize: typography.fontSize.xs,
    color: colors.text.secondary,
  },
  emptyText: {
    textAlign: 'center',
    color: colors.text.secondary,
    fontSize: typography.fontSize.sm,
    paddingVertical: spacing.xl,
  },
  searchBar: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: colors.background.secondary,
    marginHorizontal: spacing.lg,
    marginBottom: spacing.md,
    paddingHorizontal: spacing.md,
    paddingVertical: spacing.sm,
    borderRadius: 12,
    gap: spacing.sm,
  },
  searchPlaceholder: {
    flex: 1,
    fontSize: typography.fontSize.md,
    color: colors.text.secondary,
  },
});
