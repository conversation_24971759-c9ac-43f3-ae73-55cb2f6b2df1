import React, { useState } from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  TouchableOpacity,
  Alert,
  Modal,
  FlatList,
} from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import { Ionicons } from '@expo/vector-icons';
import { colors, spacing, typography } from '@/styles';
import { Input } from '@/components/common';
import { useKnowledgeStore } from '@/stores';
import { KnowledgeCategory } from '@/types';
import Toast from 'react-native-toast-message';
import { useNavigation } from '@react-navigation/native';

interface EditCategoryModalProps {
  visible: boolean;
  category: KnowledgeCategory | null;
  onClose: () => void;
  onSave: (category: KnowledgeCategory) => void;
}

const EditCategoryModal: React.FC<EditCategoryModalProps> = ({
  visible,
  category,
  onClose,
  onSave,
}) => {
  const [name, setName] = useState(category?.name || '');
  const [description, setDescription] = useState(category?.description || '');
  const [selectedColor, setSelectedColor] = useState(category?.color || colors.primary);
  const [selectedIcon, setSelectedIcon] = useState(category?.icon || 'folder');

  const colorOptions = [
    colors.primary,
    colors.success,
    colors.warning,
    colors.error,
    colors.info,
    '#9B59B6',
    '#E67E22',
    '#1ABC9C',
  ];

  const iconOptions = [
    'folder',
    'folder-outline',
    'library',
    'document-text',
    'musical-notes',
    'images',
    'briefcase',
    'school',
    'home',
    'heart',
    'star',
    'bookmark',
  ];

  React.useEffect(() => {
    if (category) {
      setName(category.name);
      setDescription(category.description || '');
      setSelectedColor(category.color || colors.primary);
      setSelectedIcon(category.icon || 'folder');
    } else {
      setName('');
      setDescription('');
      setSelectedColor(colors.primary);
      setSelectedIcon('folder');
    }
  }, [category]);

  const handleSave = () => {
    if (!name.trim()) {
      Toast.show({
        type: 'error',
        text1: '验证错误',
        text2: '分类名称不能为空',
      });
      return;
    }

    const categoryData: KnowledgeCategory = {
      id: category?.id || Date.now().toString(),
      name: name.trim(),
      description: description.trim() || undefined,
      color: selectedColor,
      icon: selectedIcon,
      order: category?.order || 0,
      createdAt: category?.createdAt || new Date(),
      updatedAt: new Date(),
      userId: 'user1',
    };

    onSave(categoryData);
    onClose();
  };

  return (
    <Modal visible={visible} animationType="slide" presentationStyle="pageSheet">
      <SafeAreaView style={styles.modalContainer}>
        <View style={styles.modalHeader}>
          <TouchableOpacity onPress={onClose}>
            <Text style={styles.modalCancel}>取消</Text>
          </TouchableOpacity>
          <Text style={styles.modalTitle}>
            {category ? '编辑分类' : '新建分类'}
          </Text>
          <TouchableOpacity onPress={handleSave}>
            <Text style={styles.modalSave}>保存</Text>
          </TouchableOpacity>
        </View>

        <ScrollView style={styles.modalContent}>
          <Input
            label="分类名称"
            value={name}
            onChangeText={setName}
            placeholder="输入分类名称"
          />

          <Input
            label="分类描述"
            value={description}
            onChangeText={setDescription}
            placeholder="输入分类描述（可选）"
            multiline
            numberOfLines={3}
          />

          {/* 颜色选择 */}
          <View style={styles.optionGroup}>
            <Text style={styles.optionLabel}>选择颜色</Text>
            <View style={styles.colorGrid}>
              {colorOptions.map(color => (
                <TouchableOpacity
                  key={color}
                  style={[
                    styles.colorOption,
                    { backgroundColor: color },
                    selectedColor === color && styles.colorOptionSelected,
                  ]}
                  onPress={() => setSelectedColor(color)}
                >
                  {selectedColor === color && (
                    <Ionicons name="checkmark" size={16} color={colors.white} />
                  )}
                </TouchableOpacity>
              ))}
            </View>
          </View>

          {/* 图标选择 */}
          <View style={styles.optionGroup}>
            <Text style={styles.optionLabel}>选择图标</Text>
            <View style={styles.iconGrid}>
              {iconOptions.map(icon => (
                <TouchableOpacity
                  key={icon}
                  style={[
                    styles.iconOption,
                    selectedIcon === icon && {
                      backgroundColor: selectedColor + '20',
                      borderColor: selectedColor,
                    },
                  ]}
                  onPress={() => setSelectedIcon(icon)}
                >
                  <Ionicons
                    name={icon as any}
                    size={24}
                    color={selectedIcon === icon ? selectedColor : colors.text.secondary}
                  />
                </TouchableOpacity>
              ))}
            </View>
          </View>

          {/* 预览 */}
          <View style={styles.optionGroup}>
            <Text style={styles.optionLabel}>预览</Text>
            <View style={styles.previewContainer}>
              <View style={[styles.previewIcon, { backgroundColor: selectedColor + '20' }]}>
                <Ionicons name={selectedIcon as any} size={24} color={selectedColor} />
              </View>
              <View style={styles.previewInfo}>
                <Text style={styles.previewName}>{name || '分类名称'}</Text>
                {description && (
                  <Text style={styles.previewDescription}>{description}</Text>
                )}
              </View>
            </View>
          </View>
        </ScrollView>
      </SafeAreaView>
    </Modal>
  );
};

export const CategoryManagementScreen: React.FC = () => {
  const navigation = useNavigation();
  const [showEditModal, setShowEditModal] = useState(false);
  const [editingCategory, setEditingCategory] = useState<KnowledgeCategory | null>(null);

  const { 
    categories, 
    documents,
    addCategory, 
    updateCategory, 
    deleteCategory 
  } = useKnowledgeStore();

  const handleAddCategory = () => {
    setEditingCategory(null);
    setShowEditModal(true);
  };

  const handleEditCategory = (category: KnowledgeCategory) => {
    setEditingCategory(category);
    setShowEditModal(true);
  };

  const handleSaveCategory = (categoryData: KnowledgeCategory) => {
    if (editingCategory) {
      updateCategory(editingCategory.id, categoryData);
      Toast.show({
        type: 'success',
        text1: '更新成功',
        text2: '分类信息已更新',
      });
    } else {
      addCategory(categoryData);
      Toast.show({
        type: 'success',
        text1: '创建成功',
        text2: '新分类已创建',
      });
    }
  };

  const handleDeleteCategory = (category: KnowledgeCategory) => {
    const categoryDocuments = documents.filter(doc => doc.categoryId === category.id);
    
    if (categoryDocuments.length > 0) {
      Alert.alert(
        '删除分类',
        `分类"${category.name}"中还有 ${categoryDocuments.length} 个文档。删除分类后，这些文档将移动到"未分类"。确定要删除吗？`,
        [
          { text: '取消', style: 'cancel' },
          {
            text: '删除',
            style: 'destructive',
            onPress: () => {
              deleteCategory(category.id);
              Toast.show({
                type: 'success',
                text1: '删除成功',
                text2: '分类已删除，文档已移动到未分类',
              });
            },
          },
        ]
      );
    } else {
      Alert.alert(
        '删除分类',
        `确定要删除分类"${category.name}"吗？`,
        [
          { text: '取消', style: 'cancel' },
          {
            text: '删除',
            style: 'destructive',
            onPress: () => {
              deleteCategory(category.id);
              Toast.show({
                type: 'success',
                text1: '删除成功',
                text2: '分类已删除',
              });
            },
          },
        ]
      );
    }
  };

  const getCategoryDocumentCount = (categoryId: string): number => {
    return documents.filter(doc => doc.categoryId === categoryId).length;
  };

  const renderCategoryItem = ({ item }: { item: KnowledgeCategory }) => {
    const documentCount = getCategoryDocumentCount(item.id);
    
    return (
      <View style={[styles.categoryItem, { borderLeftColor: item.color || colors.primary }]}>
        <View style={styles.categoryMain}>
          <View style={[
            styles.categoryIcon, 
            { backgroundColor: (item.color || colors.primary) + '20' }
          ]}>
            <Ionicons
              name={(item.icon || 'folder') as any}
              size={24}
              color={item.color || colors.primary}
            />
          </View>
          
          <View style={styles.categoryInfo}>
            <Text style={styles.categoryName}>{item.name}</Text>
            {item.description && (
              <Text style={styles.categoryDescription}>{item.description}</Text>
            )}
            <Text style={styles.categoryMeta}>
              {documentCount} 个文档 • {new Date(item.createdAt).toLocaleDateString('zh-CN')}
            </Text>
          </View>
          
          <View style={styles.categoryCount}>
            <Text style={styles.categoryCountText}>{documentCount}</Text>
          </View>
        </View>
        
        <View style={styles.categoryActions}>
          <TouchableOpacity
            style={styles.actionButton}
            onPress={() => handleEditCategory(item)}
          >
            <Ionicons name="create" size={20} color={colors.primary} />
            <Text style={styles.actionButtonText}>编辑</Text>
          </TouchableOpacity>
          
          <TouchableOpacity
            style={styles.actionButton}
            onPress={() => handleDeleteCategory(item)}
          >
            <Ionicons name="trash" size={20} color={colors.error} />
            <Text style={[styles.actionButtonText, { color: colors.error }]}>删除</Text>
          </TouchableOpacity>
        </View>
      </View>
    );
  };

  const renderEmptyState = () => (
    <View style={styles.emptyContainer}>
      <Ionicons name="folder-open-outline" size={64} color={colors.text.secondary} />
      <Text style={styles.emptyTitle}>暂无分类</Text>
      <Text style={styles.emptySubtitle}>创建分类来组织您的文档</Text>
      <TouchableOpacity style={styles.emptyButton} onPress={handleAddCategory}>
        <Ionicons name="add" size={20} color={colors.white} />
        <Text style={styles.emptyButtonText}>创建第一个分类</Text>
      </TouchableOpacity>
    </View>
  );

  return (
    <SafeAreaView style={styles.container}>
      <View style={styles.header}>
        <TouchableOpacity onPress={() => navigation.goBack()}>
          <Ionicons name="chevron-back" size={24} color={colors.text.primary} />
        </TouchableOpacity>
        <Text style={styles.title}>分类管理</Text>
        <TouchableOpacity onPress={handleAddCategory}>
          <Ionicons name="add" size={24} color={colors.primary} />
        </TouchableOpacity>
      </View>

      {/* 统计信息 */}
      <View style={styles.statsContainer}>
        <View style={styles.statItem}>
          <Text style={styles.statNumber}>{categories.length}</Text>
          <Text style={styles.statLabel}>总分类</Text>
        </View>
        <View style={styles.statItem}>
          <Text style={styles.statNumber}>{documents.length}</Text>
          <Text style={styles.statLabel}>总文档</Text>
        </View>
        <View style={styles.statItem}>
          <Text style={styles.statNumber}>
            {documents.filter(doc => !doc.categoryId).length}
          </Text>
          <Text style={styles.statLabel}>未分类</Text>
        </View>
      </View>

      {categories.length === 0 ? (
        renderEmptyState()
      ) : (
        <FlatList
          data={categories}
          renderItem={renderCategoryItem}
          keyExtractor={item => item.id}
          contentContainerStyle={styles.categoryList}
          showsVerticalScrollIndicator={false}
        />
      )}

      {categories.length > 0 && (
        <TouchableOpacity style={styles.addButton} onPress={handleAddCategory}>
          <Ionicons name="add" size={24} color={colors.white} />
          <Text style={styles.addButtonText}>新建分类</Text>
        </TouchableOpacity>
      )}

      <EditCategoryModal
        visible={showEditModal}
        category={editingCategory}
        onClose={() => setShowEditModal(false)}
        onSave={handleSaveCategory}
      />
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: colors.background.primary,
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: spacing.lg,
    paddingVertical: spacing.md,
    borderBottomWidth: 1,
    borderBottomColor: colors.border,
  },
  title: {
    fontSize: typography.fontSize.xl,
    fontWeight: typography.fontWeight.semibold as any,
    color: colors.text.primary,
  },
  statsContainer: {
    flexDirection: 'row',
    paddingHorizontal: spacing.lg,
    paddingVertical: spacing.lg,
    backgroundColor: colors.background.secondary,
    marginBottom: spacing.sm,
  },
  statItem: {
    flex: 1,
    alignItems: 'center',
  },
  statNumber: {
    fontSize: typography.fontSize.xxl,
    fontWeight: typography.fontWeight.bold as any,
    color: colors.primary,
  },
  statLabel: {
    fontSize: typography.fontSize.sm,
    color: colors.text.secondary,
    marginTop: spacing.xs,
  },
  categoryList: {
    padding: spacing.lg,
  },
  categoryItem: {
    backgroundColor: colors.background.secondary,
    borderRadius: 12,
    marginBottom: spacing.md,
    borderLeftWidth: 4,
    overflow: 'hidden',
  },
  categoryMain: {
    flexDirection: 'row',
    alignItems: 'center',
    padding: spacing.md,
  },
  categoryIcon: {
    width: 48,
    height: 48,
    borderRadius: 24,
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: spacing.md,
  },
  categoryInfo: {
    flex: 1,
  },
  categoryName: {
    fontSize: typography.fontSize.lg,
    fontWeight: typography.fontWeight.semibold as any,
    color: colors.text.primary,
    marginBottom: spacing.xs,
  },
  categoryDescription: {
    fontSize: typography.fontSize.sm,
    color: colors.text.secondary,
    marginBottom: spacing.xs,
  },
  categoryMeta: {
    fontSize: typography.fontSize.xs,
    color: colors.text.secondary,
  },
  categoryCount: {
    backgroundColor: colors.background.tertiary,
    paddingHorizontal: spacing.sm,
    paddingVertical: spacing.xs,
    borderRadius: 16,
  },
  categoryCountText: {
    fontSize: typography.fontSize.sm,
    fontWeight: typography.fontWeight.medium as any,
    color: colors.text.primary,
  },
  categoryActions: {
    flexDirection: 'row',
    paddingHorizontal: spacing.md,
    paddingBottom: spacing.md,
    gap: spacing.md,
  },
  actionButton: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: colors.background.tertiary,
    paddingHorizontal: spacing.md,
    paddingVertical: spacing.sm,
    borderRadius: 8,
    gap: spacing.xs,
  },
  actionButtonText: {
    fontSize: typography.fontSize.sm,
    color: colors.primary,
  },
  addButton: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    backgroundColor: colors.primary,
    marginHorizontal: spacing.lg,
    marginBottom: spacing.lg,
    paddingVertical: spacing.md,
    borderRadius: 12,
    gap: spacing.sm,
  },
  addButtonText: {
    fontSize: typography.fontSize.md,
    fontWeight: typography.fontWeight.medium as any,
    color: colors.white,
  },
  emptyContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    paddingHorizontal: spacing.xl,
  },
  emptyTitle: {
    fontSize: typography.fontSize.xl,
    fontWeight: typography.fontWeight.semibold as any,
    color: colors.text.primary,
    marginTop: spacing.lg,
    marginBottom: spacing.sm,
  },
  emptySubtitle: {
    fontSize: typography.fontSize.md,
    color: colors.text.secondary,
    textAlign: 'center',
    marginBottom: spacing.xl,
  },
  emptyButton: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: colors.primary,
    paddingHorizontal: spacing.lg,
    paddingVertical: spacing.md,
    borderRadius: 12,
    gap: spacing.sm,
  },
  emptyButtonText: {
    fontSize: typography.fontSize.md,
    fontWeight: typography.fontWeight.medium as any,
    color: colors.white,
  },
  modalContainer: {
    flex: 1,
    backgroundColor: colors.background.primary,
  },
  modalHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: spacing.lg,
    paddingVertical: spacing.md,
    borderBottomWidth: 1,
    borderBottomColor: colors.border,
  },
  modalCancel: {
    fontSize: typography.fontSize.md,
    color: colors.text.secondary,
  },
  modalTitle: {
    fontSize: typography.fontSize.lg,
    fontWeight: typography.fontWeight.semibold as any,
    color: colors.text.primary,
  },
  modalSave: {
    fontSize: typography.fontSize.md,
    color: colors.primary,
    fontWeight: typography.fontWeight.medium as any,
  },
  modalContent: {
    flex: 1,
    padding: spacing.lg,
  },
  optionGroup: {
    marginBottom: spacing.xl,
  },
  optionLabel: {
    fontSize: typography.fontSize.md,
    fontWeight: typography.fontWeight.medium as any,
    color: colors.text.primary,
    marginBottom: spacing.md,
  },
  colorGrid: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    gap: spacing.sm,
  },
  colorOption: {
    width: 40,
    height: 40,
    borderRadius: 20,
    justifyContent: 'center',
    alignItems: 'center',
    borderWidth: 2,
    borderColor: 'transparent',
  },
  colorOptionSelected: {
    borderColor: colors.border,
  },
  iconGrid: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    gap: spacing.sm,
  },
  iconOption: {
    width: 48,
    height: 48,
    borderRadius: 12,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: colors.background.secondary,
    borderWidth: 2,
    borderColor: 'transparent',
  },
  previewContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: colors.background.secondary,
    padding: spacing.md,
    borderRadius: 12,
    gap: spacing.md,
  },
  previewIcon: {
    width: 48,
    height: 48,
    borderRadius: 24,
    justifyContent: 'center',
    alignItems: 'center',
  },
  previewInfo: {
    flex: 1,
  },
  previewName: {
    fontSize: typography.fontSize.md,
    fontWeight: typography.fontWeight.medium as any,
    color: colors.text.primary,
    marginBottom: spacing.xs,
  },
  previewDescription: {
    fontSize: typography.fontSize.sm,
    color: colors.text.secondary,
  },
});