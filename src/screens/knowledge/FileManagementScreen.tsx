import React, { useState } from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  TouchableOpacity,
  Alert,
  ActivityIndicator,
  Modal,
  FlatList,
  RefreshControl,
} from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import { Ionicons } from '@expo/vector-icons';
import { colors, spacing, typography } from '@/styles';
import { Input } from '@/components/common';
import { useKnowledgeStore } from '@/stores';
import { KnowledgeDocument } from '@/types';
import Toast from 'react-native-toast-message';
import { useNavigation } from '@react-navigation/native';
import * as Sharing from 'expo-sharing';

type SortOption = 'name' | 'date' | 'size' | 'type';
type ViewMode = 'list' | 'grid';

interface FilterOptions {
  category: string;
  type: string;
  dateRange: string;
}

export const FileManagementScreen: React.FC = () => {
  const navigation = useNavigation();
  const [refreshing, setRefreshing] = useState(false);
  const [sortBy, setSortBy] = useState<SortOption>('date');
  const [viewMode, setViewMode] = useState<ViewMode>('list');
  const [showEditModal, setShowEditModal] = useState(false);
  const [selectedDocument, setSelectedDocument] = useState<KnowledgeDocument | null>(null);
  const [editingTitle, setEditingTitle] = useState('');
  const [editingDescription, setEditingDescription] = useState('');
  const [editingTags, setEditingTags] = useState('');
  const [selectedDocuments, setSelectedDocuments] = useState<string[]>([]);
  const [isSelectionMode, setIsSelectionMode] = useState(false);
  const [searchQuery, setSearchQuery] = useState('');
  const [filters, setFilters] = useState<FilterOptions>({
    category: '',
    type: '',
    dateRange: ''
  });
  const [showFilter, setShowFilter] = useState(false);
  
  const { 
    documents, 
    updateDocument, 
    deleteDocument, 
    isLoading 
  } = useKnowledgeStore();

  const filteredAndSortedDocuments = React.useMemo(() => {
    let filtered = documents.filter(doc => {
      const matchesSearch = doc.title.toLowerCase().includes(searchQuery.toLowerCase()) ||
                          doc.content.toLowerCase().includes(searchQuery.toLowerCase()) ||
                          doc.tags.some(tag => tag.toLowerCase().includes(searchQuery.toLowerCase()));
      
      const matchesCategory = !filters.category || doc.categoryId === filters.category;
      const matchesType = !filters.type || doc.type === filters.type;

      return matchesSearch && matchesCategory && matchesType;
    });

    filtered.sort((a, b) => {
      switch (sortBy) {
        case 'name':
          return a.title.localeCompare(b.title);
        case 'date':
          return new Date(b.createdAt).getTime() - new Date(a.createdAt).getTime();
        case 'size':
          return b.size - a.size;
        case 'type':
          return a.type.localeCompare(b.type);
        default:
          return 0;
      }
    });

    return filtered;
  }, [documents, searchQuery, filters, sortBy]);

  const onRefresh = async () => {
    setRefreshing(true);
    // 模拟刷新操作
    await new Promise(resolve => setTimeout(resolve, 1000));
    setRefreshing(false);
  };

  const handleDocumentPress = (document: KnowledgeDocument) => {
    if (isSelectionMode) {
      toggleDocumentSelection(document.id);
    } else {
      // 打开文档详情或预览
      Alert.alert('文档详情', `打开文档: ${document.title}`);
    }
  };

  const handleDocumentLongPress = (document: KnowledgeDocument) => {
    if (!isSelectionMode) {
      setIsSelectionMode(true);
      setSelectedDocuments([document.id]);
    }
  };

  const toggleDocumentSelection = (documentId: string) => {
    setSelectedDocuments(prev => 
      prev.includes(documentId) 
        ? prev.filter(id => id !== documentId)
        : [...prev, documentId]
    );
  };

  const exitSelectionMode = () => {
    setIsSelectionMode(false);
    setSelectedDocuments([]);
  };

  const selectAllDocuments = () => {
    if (selectedDocuments.length === filteredAndSortedDocuments.length) {
      setSelectedDocuments([]);
    } else {
      setSelectedDocuments(filteredAndSortedDocuments.map(doc => doc.id));
    }
  };

  const handleEditDocument = (document: KnowledgeDocument) => {
    setSelectedDocument(document);
    setEditingTitle(document.title);
    setEditingDescription(document.description || '');
    setEditingTags(document.tags.join(', '));
    setShowEditModal(true);
  };

  const handleSaveDocument = async () => {
    if (!selectedDocument) return;

    try {
      const updates = {
        title: editingTitle,
        description: editingDescription,
        tags: editingTags.split(',').map(tag => tag.trim()).filter(Boolean),
        updatedAt: new Date(),
      };

      updateDocument(selectedDocument.id, updates);
      setShowEditModal(false);
      setSelectedDocument(null);
      
      Toast.show({
        type: 'success',
        text1: '保存成功',
        text2: '文档信息已更新',
      });
    } catch (error) {
      Toast.show({
        type: 'error',
        text1: '保存失败',
        text2: '无法更新文档信息',
      });
    }
  };

  const handleDeleteDocument = (document: KnowledgeDocument) => {
    Alert.alert(
      '删除文档',
      `确定要删除文档"${document.title}"吗？此操作无法撤销。`,
      [
        { text: '取消', style: 'cancel' },
        {
          text: '删除',
          style: 'destructive',
          onPress: () => {
            deleteDocument(document.id);
            Toast.show({
              type: 'success',
              text1: '删除成功',
              text2: '文档已从知识库中删除',
            });
          },
        },
      ]
    );
  };

  const handleBatchDelete = () => {
    Alert.alert(
      '批量删除',
      `确定要删除选中的 ${selectedDocuments.length} 个文档吗？此操作无法撤销。`,
      [
        { text: '取消', style: 'cancel' },
        {
          text: '删除',
          style: 'destructive',
          onPress: () => {
            selectedDocuments.forEach(id => deleteDocument(id));
            exitSelectionMode();
            Toast.show({
              type: 'success',
              text1: '删除成功',
              text2: `已删除 ${selectedDocuments.length} 个文档`,
            });
          },
        },
      ]
    );
  };

  const handleShareDocument = async (document: KnowledgeDocument) => {
    try {
      if (await Sharing.isAvailableAsync()) {
        await Sharing.shareAsync(document.filePath, {
          mimeType: getMimeType(document.type),
          dialogTitle: `分享文档: ${document.title}`,
        });
      } else {
        Toast.show({
          type: 'info',
          text1: '分享功能不可用',
          text2: '当前平台不支持文件分享',
        });
      }
    } catch (error) {
      Toast.show({
        type: 'error',
        text1: '分享失败',
        text2: '无法分享文档',
      });
    }
  };

  const getMimeType = (type: KnowledgeDocument['type']): string => {
    const mimeTypes = {
      pdf: 'application/pdf',
      word: 'application/msword',
      text: 'text/plain',
      audio: 'audio/mpeg',
      image: 'image/jpeg',
      excel: 'application/vnd.ms-excel',
      ppt: 'application/vnd.ms-powerpoint',
      other: 'application/octet-stream',
    };
    return mimeTypes[type] || mimeTypes.other;
  };

  const formatFileSize = (bytes: number): string => {
    if (bytes === 0) return '0 B';
    const k = 1024;
    const sizes = ['B', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  };

  const getDocumentIcon = (type: KnowledgeDocument['type']) => {
    const icons = {
      pdf: 'document-text',
      word: 'document',
      text: 'document-outline',
      audio: 'musical-notes',
      image: 'image',
      excel: 'grid',
      ppt: 'easel',
      other: 'document',
    };
    return icons[type] || icons.other;
  };

  const renderDocumentItem = ({ item }: { item: KnowledgeDocument }) => {
    const isSelected = selectedDocuments.includes(item.id);
    
    return (
      <TouchableOpacity
        style={[
          styles.documentItem,
          isSelected && styles.documentItemSelected,
          viewMode === 'grid' && styles.documentItemGrid
        ]}
        onPress={() => handleDocumentPress(item)}
        onLongPress={() => handleDocumentLongPress(item)}
      >
        {isSelectionMode && (
          <TouchableOpacity
            style={styles.checkbox}
            onPress={() => toggleDocumentSelection(item.id)}
          >
            <Ionicons
              name={isSelected ? 'checkbox' : 'square-outline'}
              size={20}
              color={isSelected ? colors.primary : colors.text.secondary}
            />
          </TouchableOpacity>
        )}
        
        <View style={[styles.documentIcon, { backgroundColor: colors.primary + '20' }]}>
          <Ionicons
            name={getDocumentIcon(item.type) as any}
            size={viewMode === 'grid' ? 32 : 24}
            color={colors.primary}
          />
        </View>
        
        <View style={styles.documentInfo}>
          <Text style={styles.documentTitle} numberOfLines={2}>
            {item.title}
          </Text>
          <Text style={styles.documentMeta}>
            {formatFileSize(item.size)} • {new Date(item.createdAt).toLocaleDateString('zh-CN')}
          </Text>
          {item.tags.length > 0 && (
            <View style={styles.tagContainer}>
              {item.tags.slice(0, 3).map((tag, index) => (
                <Text key={index} style={styles.tag}>
                  {tag}
                </Text>
              ))}
              {item.tags.length > 3 && (
                <Text style={styles.tagMore}>+{item.tags.length - 3}</Text>
              )}
            </View>
          )}
        </View>
        
        {!isSelectionMode && (
          <TouchableOpacity
            style={styles.moreButton}
            onPress={() => showDocumentActions(item)}
          >
            <Ionicons name="ellipsis-vertical" size={20} color={colors.text.secondary} />
          </TouchableOpacity>
        )}
      </TouchableOpacity>
    );
  };

  const showDocumentActions = (document: KnowledgeDocument) => {
    Alert.alert(
      document.title,
      '选择操作',
      [
        { text: '编辑', onPress: () => handleEditDocument(document) },
        { text: '分享', onPress: () => navigation.navigate('DocumentShare' as any, { documentId: document.id }) },
        { text: '删除', style: 'destructive', onPress: () => handleDeleteDocument(document) },
        { text: '取消', style: 'cancel' },
      ]
    );
  };

  const renderSortOption = (option: SortOption, label: string) => (
    <TouchableOpacity
      key={option}
      style={[styles.sortOption, sortBy === option && styles.sortOptionSelected]}
      onPress={() => setSortBy(option)}
    >
      <Text style={[
        styles.sortOptionText,
        sortBy === option && styles.sortOptionTextSelected
      ]}>
        {label}
      </Text>
    </TouchableOpacity>
  );

  const renderHeader = () => (
    <View style={styles.header}>
      {isSelectionMode ? (
        <View style={styles.selectionHeader}>
          <TouchableOpacity onPress={exitSelectionMode}>
            <Ionicons name="close" size={24} color={colors.text.primary} />
          </TouchableOpacity>
          <Text style={styles.selectionCount}>
            已选择 {selectedDocuments.length} 项
          </Text>
          <View style={styles.selectionActions}>
            <TouchableOpacity onPress={selectAllDocuments} style={styles.selectionAction}>
              <Ionicons name="checkmark-done" size={20} color={colors.primary} />
            </TouchableOpacity>
            <TouchableOpacity onPress={handleBatchDelete} style={styles.selectionAction}>
              <Ionicons name="trash" size={20} color={colors.error} />
            </TouchableOpacity>
          </View>
        </View>
      ) : (
        <>
          <TouchableOpacity onPress={() => navigation.goBack()}>
            <Ionicons name="chevron-back" size={24} color={colors.text.primary} />
          </TouchableOpacity>
          <Text style={styles.title}>文件管理</Text>
          <View style={styles.headerActions}>
            <TouchableOpacity 
              onPress={() => setViewMode(viewMode === 'list' ? 'grid' : 'list')}
              style={styles.headerAction}
            >
              <Ionicons 
                name={viewMode === 'list' ? 'grid' : 'list'} 
                size={20} 
                color={colors.text.primary} 
              />
            </TouchableOpacity>
            <TouchableOpacity 
              onPress={() => navigation.navigate('DocumentCollaboration' as any)}
              style={styles.headerAction}
            >
              <Ionicons name="people" size={20} color={colors.text.primary} />
            </TouchableOpacity>
            <TouchableOpacity 
              onPress={() => setShowFilter(true)}
              style={styles.headerAction}
            >
              <Ionicons name="funnel" size={20} color={colors.text.primary} />
            </TouchableOpacity>
          </View>
        </>
      )}
    </View>
  );

  return (
    <SafeAreaView style={styles.container}>
      {renderHeader()}

      {/* 搜索栏 */}
      {!isSelectionMode && (
        <View style={styles.searchContainer}>
          <TouchableOpacity 
            style={styles.searchBox}
            onPress={() => navigation.navigate('Search' as any)}
          >
            <Ionicons name="search" size={20} color={colors.text.secondary} />
            <Text style={styles.searchPlaceholder}>搜索文档内容...</Text>
          </TouchableOpacity>
          <TouchableOpacity
            style={styles.advancedSearchButton}
            onPress={() => navigation.navigate('Search' as any)}
          >
            <Ionicons name="funnel" size={20} color={colors.primary} />
          </TouchableOpacity>
        </View>
      )}

      {/* 排序选项 */}
      {!isSelectionMode && (
        <ScrollView 
          horizontal 
          showsHorizontalScrollIndicator={false} 
          style={styles.sortContainer}
        >
          {renderSortOption('date', '按时间')}
          {renderSortOption('name', '按名称')}
          {renderSortOption('size', '按大小')}
          {renderSortOption('type', '按类型')}
        </ScrollView>
      )}

      {/* 文档列表 */}
      {isLoading ? (
        <View style={styles.loadingContainer}>
          <ActivityIndicator size="large" color={colors.primary} />
          <Text style={styles.loadingText}>加载文档...</Text>
        </View>
      ) : (
        <FlatList
          data={filteredAndSortedDocuments}
          renderItem={renderDocumentItem}
          keyExtractor={item => item.id}
          numColumns={viewMode === 'grid' ? 2 : 1}
          key={viewMode}
          contentContainerStyle={styles.documentList}
          refreshControl={
            <RefreshControl refreshing={refreshing} onRefresh={onRefresh} />
          }
          ListEmptyComponent={
            <View style={styles.emptyContainer}>
              <Ionicons name="folder-open-outline" size={64} color={colors.text.secondary} />
              <Text style={styles.emptyText}>没有找到文档</Text>
              <Text style={styles.emptySubtext}>
                {searchQuery ? '尝试调整搜索条件' : '开始上传文档到知识库'}
              </Text>
            </View>
          }
        />
      )}

      {/* 编辑文档模态框 */}
      <Modal
        visible={showEditModal}
        animationType="slide"
        presentationStyle="pageSheet"
      >
        <SafeAreaView style={styles.modalContainer}>
          <View style={styles.modalHeader}>
            <TouchableOpacity onPress={() => setShowEditModal(false)}>
              <Text style={styles.modalCancel}>取消</Text>
            </TouchableOpacity>
            <Text style={styles.modalTitle}>编辑文档</Text>
            <TouchableOpacity onPress={handleSaveDocument}>
              <Text style={styles.modalSave}>保存</Text>
            </TouchableOpacity>
          </View>
          
          <ScrollView style={styles.modalContent}>
            <Input
              label="文档标题"
              value={editingTitle}
              onChangeText={setEditingTitle}
              placeholder="输入文档标题"
            />
            
            <Input
              label="文档描述"
              value={editingDescription}
              onChangeText={setEditingDescription}
              placeholder="输入文档描述"
              multiline
              numberOfLines={3}
            />
            
            <Input
              label="标签"
              value={editingTags}
              onChangeText={setEditingTags}
              placeholder="输入标签，用逗号分隔"
            />
          </ScrollView>
        </SafeAreaView>
      </Modal>
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: colors.background.primary,
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: spacing.lg,
    paddingVertical: spacing.md,
    borderBottomWidth: 1,
    borderBottomColor: colors.border,
  },
  title: {
    flex: 1,
    textAlign: 'center',
    fontSize: typography.fontSize.xl,
    fontWeight: typography.fontWeight.semibold as any,
    color: colors.text.primary,
    marginHorizontal: spacing.md,
  },
  headerActions: {
    flexDirection: 'row',
    gap: spacing.sm,
  },
  headerAction: {
    padding: spacing.sm,
  },
  selectionHeader: {
    flex: 1,
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
  },
  selectionCount: {
    flex: 1,
    textAlign: 'center',
    fontSize: typography.fontSize.md,
    color: colors.text.primary,
  },
  selectionActions: {
    flexDirection: 'row',
    gap: spacing.sm,
  },
  selectionAction: {
    padding: spacing.sm,
  },
  searchContainer: {
    flexDirection: 'row',
    paddingHorizontal: spacing.lg,
    paddingBottom: spacing.md,
    gap: spacing.sm,
  },
  searchBox: {
    flex: 1,
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: colors.background.secondary,
    borderRadius: 12,
    paddingHorizontal: spacing.md,
    paddingVertical: spacing.sm,
    gap: spacing.sm,
  },
  searchPlaceholder: {
    flex: 1,
    fontSize: typography.fontSize.md,
    color: colors.text.secondary,
    paddingVertical: spacing.xs,
  },
  advancedSearchButton: {
    backgroundColor: colors.background.secondary,
    padding: spacing.sm,
    borderRadius: 12,
    justifyContent: 'center',
    alignItems: 'center',
  },
  sortContainer: {
    paddingHorizontal: spacing.lg,
    marginBottom: spacing.md,
  },
  sortOption: {
    paddingHorizontal: spacing.md,
    paddingVertical: spacing.sm,
    borderRadius: 20,
    backgroundColor: colors.background.secondary,
    marginRight: spacing.sm,
    borderWidth: 1,
    borderColor: colors.border,
  },
  sortOptionSelected: {
    backgroundColor: colors.primary,
    borderColor: colors.primary,
  },
  sortOptionText: {
    fontSize: typography.fontSize.sm,
    color: colors.text.secondary,
  },
  sortOptionTextSelected: {
    color: colors.white,
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  loadingText: {
    marginTop: spacing.md,
    fontSize: typography.fontSize.md,
    color: colors.text.secondary,
  },
  documentList: {
    padding: spacing.lg,
  },
  documentItem: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: colors.background.secondary,
    padding: spacing.md,
    borderRadius: 12,
    marginBottom: spacing.sm,
    gap: spacing.sm,
  },
  documentItemSelected: {
    borderWidth: 2,
    borderColor: colors.primary,
  },
  documentItemGrid: {
    flex: 1,
    marginHorizontal: spacing.xs,
    flexDirection: 'column',
    alignItems: 'stretch',
  },
  checkbox: {
    padding: spacing.xs,
  },
  documentIcon: {
    width: 48,
    height: 48,
    borderRadius: 24,
    justifyContent: 'center',
    alignItems: 'center',
  },
  documentInfo: {
    flex: 1,
  },
  documentTitle: {
    fontSize: typography.fontSize.md,
    fontWeight: typography.fontWeight.medium as any,
    color: colors.text.primary,
    marginBottom: spacing.xs,
  },
  documentMeta: {
    fontSize: typography.fontSize.xs,
    color: colors.text.secondary,
    marginBottom: spacing.xs,
  },
  tagContainer: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    gap: spacing.xs,
  },
  tag: {
    fontSize: typography.fontSize.xs,
    color: colors.primary,
    backgroundColor: colors.primary + '20',
    paddingHorizontal: spacing.xs,
    paddingVertical: 2,
    borderRadius: 8,
  },
  tagMore: {
    fontSize: typography.fontSize.xs,
    color: colors.text.secondary,
  },
  moreButton: {
    padding: spacing.sm,
  },
  emptyContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    paddingVertical: spacing.xxl,
  },
  emptyText: {
    fontSize: typography.fontSize.lg,
    fontWeight: typography.fontWeight.medium as any,
    color: colors.text.secondary,
    marginTop: spacing.md,
    marginBottom: spacing.sm,
  },
  emptySubtext: {
    fontSize: typography.fontSize.sm,
    color: colors.text.secondary,
    textAlign: 'center',
  },
  modalContainer: {
    flex: 1,
    backgroundColor: colors.background.primary,
  },
  modalHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: spacing.lg,
    paddingVertical: spacing.md,
    borderBottomWidth: 1,
    borderBottomColor: colors.border,
  },
  modalCancel: {
    fontSize: typography.fontSize.md,
    color: colors.text.secondary,
  },
  modalTitle: {
    fontSize: typography.fontSize.lg,
    fontWeight: typography.fontWeight.semibold as any,
    color: colors.text.primary,
  },
  modalSave: {
    fontSize: typography.fontSize.md,
    color: colors.primary,
    fontWeight: typography.fontWeight.medium as any,
  },
  modalContent: {
    flex: 1,
    padding: spacing.lg,
  },
});