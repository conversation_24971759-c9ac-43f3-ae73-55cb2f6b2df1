import React, { useState, useEffect, useRef } from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  TouchableOpacity,
  TextInput,
  Alert,
  Modal,
  KeyboardAvoidingView,
  Platform,
  ActivityIndicator,
} from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import { Ionicons } from '@expo/vector-icons';
import { useNavigation, useRoute } from '@react-navigation/native';
import { colors, spacing, typography } from '@/styles';
import { useKnowledgeStore } from '@/stores';
import { KnowledgeDocument, KnowledgeCategory } from '@/types';
import Toast from 'react-native-toast-message';

interface EditingHistory {
  id: string;
  content: string;
  timestamp: Date;
}

export const DocumentEditorScreen: React.FC = () => {
  const navigation = useNavigation();
  const route = useRoute();
  const { documentId } = route.params as { documentId?: string };
  const { documents, categories, addDocument, updateDocument } = useKnowledgeStore();
  
  const [document, setDocument] = useState<KnowledgeDocument | null>(null);
  const [title, setTitle] = useState('');
  const [content, setContent] = useState('');
  const [description, setDescription] = useState('');
  const [tags, setTags] = useState<string[]>([]);
  const [categoryId, setCategoryId] = useState<string>('');
  const [tagInput, setTagInput] = useState('');
  const [isLoading, setIsLoading] = useState(false);
  const [isSaving, setIsSaving] = useState(false);
  const [showFormatting, setShowFormatting] = useState(false);
  const [showCategoryModal, setShowCategoryModal] = useState(false);
  const [showTagModal, setShowTagModal] = useState(false);
  const [history, setHistory] = useState<EditingHistory[]>([]);
  const [currentHistoryIndex, setCurrentHistoryIndex] = useState(-1);
  const [wordCount, setWordCount] = useState(0);
  const [selectionStart, setSelectionStart] = useState(0);
  const [selectionEnd, setSelectionEnd] = useState(0);
  
  const contentInputRef = useRef<TextInput>(null);

  useEffect(() => {
    if (documentId) {
      const doc = documents.find(d => d.id === documentId);
      if (doc) {
        setDocument(doc);
        setTitle(doc.title);
        setContent(doc.content);
        setDescription(doc.description || '');
        setTags(doc.tags);
        setCategoryId(doc.categoryId || '');
        addToHistory(doc.content);
      }
    } else {
      // 新建文档
      addToHistory('');
    }
  }, [documentId, documents]);

  useEffect(() => {
    const count = content.replace(/\s/g, '').length;
    setWordCount(count);
  }, [content]);

  const addToHistory = (newContent: string) => {
    const newHistory: EditingHistory = {
      id: Date.now().toString(),
      content: newContent,
      timestamp: new Date(),
    };
    
    const updatedHistory = [...history.slice(0, currentHistoryIndex + 1), newHistory];
    setHistory(updatedHistory);
    setCurrentHistoryIndex(updatedHistory.length - 1);
  };

  const handleUndo = () => {
    if (currentHistoryIndex > 0) {
      const newIndex = currentHistoryIndex - 1;
      setCurrentHistoryIndex(newIndex);
      setContent(history[newIndex].content);
    }
  };

  const handleRedo = () => {
    if (currentHistoryIndex < history.length - 1) {
      const newIndex = currentHistoryIndex + 1;
      setCurrentHistoryIndex(newIndex);
      setContent(history[newIndex].content);
    }
  };

  const handleContentChange = (newContent: string) => {
    setContent(newContent);
    // 延迟添加到历史记录，避免频繁更新
    const timeoutId = setTimeout(() => {
      if (newContent !== history[currentHistoryIndex]?.content) {
        addToHistory(newContent);
      }
    }, 1000);
    
    return () => clearTimeout(timeoutId);
  };

  const insertFormatting = (format: string) => {
    if (!contentInputRef.current) return;
    
    const beforeText = content.substring(0, selectionStart);
    const selectedText = content.substring(selectionStart, selectionEnd);
    const afterText = content.substring(selectionEnd);
    
    let newText = '';
    
    switch (format) {
      case 'bold':
        newText = `${beforeText}**${selectedText}**${afterText}`;
        break;
      case 'italic':
        newText = `${beforeText}_${selectedText}_${afterText}`;
        break;
      case 'heading':
        newText = `${beforeText}# ${selectedText}${afterText}`;
        break;
      case 'list':
        newText = `${beforeText}\n- ${selectedText}${afterText}`;
        break;
      case 'quote':
        newText = `${beforeText}\n> ${selectedText}${afterText}`;
        break;
      default:
        return;
    }
    
    setContent(newText);
    addToHistory(newText);
  };

  const handleAddTag = () => {
    if (tagInput.trim() && !tags.includes(tagInput.trim())) {
      setTags([...tags, tagInput.trim()]);
      setTagInput('');
      setShowTagModal(false);
    }
  };

  const handleRemoveTag = (tagToRemove: string) => {
    setTags(tags.filter(tag => tag !== tagToRemove));
  };

  const handleSave = async () => {
    if (!title.trim()) {
      Toast.show({
        type: 'error',
        text1: '标题不能为空',
      });
      return;
    }

    setIsSaving(true);
    
    try {
      const now = new Date();
      
      if (document) {
        // 更新现有文档
        const updates: Partial<KnowledgeDocument> = {
          title: title.trim(),
          content: content.trim(),
          description: description.trim(),
          tags,
          categoryId: categoryId || undefined,
          updatedAt: now,
        };
        
        updateDocument(document.id, updates);
        Toast.show({
          type: 'success',
          text1: '文档已更新',
        });
      } else {
        // 创建新文档
        const newDocument: KnowledgeDocument = {
          id: Date.now().toString(),
          title: title.trim(),
          content: content.trim(),
          description: description.trim(),
          type: 'text',
          size: content.length,
          filePath: '',
          tags,
          categoryId: categoryId || undefined,
          createdAt: now,
          updatedAt: now,
          userId: 'current-user',
        };
        
        addDocument(newDocument);
        setDocument(newDocument);
        Toast.show({
          type: 'success',
          text1: '文档已创建',
        });
      }
    } catch (error) {
      Toast.show({
        type: 'error',
        text1: '保存失败',
        text2: '请重试',
      });
    } finally {
      setIsSaving(false);
    }
  };

  const handleExit = () => {
    const hasChanges = document ? 
      (title !== document.title || content !== document.content || 
       description !== (document.description || '') || 
       JSON.stringify(tags) !== JSON.stringify(document.tags)) :
      (title.trim() || content.trim() || description.trim() || tags.length > 0);

    if (hasChanges) {
      Alert.alert(
        '确认退出',
        '您有未保存的更改，确定要退出吗？',
        [
          { text: '保存并退出', onPress: async () => { await handleSave(); navigation.goBack(); } },
          { text: '不保存', style: 'destructive', onPress: () => navigation.goBack() },
          { text: '取消', style: 'cancel' },
        ]
      );
    } else {
      navigation.goBack();
    }
  };

  const renderFormattingToolbar = () => (
    <Modal
      visible={showFormatting}
      animationType="slide"
      presentationStyle="overFullScreen"
      transparent
    >
      <View style={styles.formattingModal}>
        <View style={styles.formattingToolbar}>
          <TouchableOpacity
            style={styles.formattingButton}
            onPress={() => insertFormatting('bold')}
          >
            <Ionicons name="text" size={20} color={colors.text.primary} />
            <Text style={styles.formattingLabel}>粗体</Text>
          </TouchableOpacity>
          
          <TouchableOpacity
            style={styles.formattingButton}
            onPress={() => insertFormatting('italic')}
          >
            <Ionicons name="text" size={20} color={colors.text.primary} />
            <Text style={styles.formattingLabel}>斜体</Text>
          </TouchableOpacity>
          
          <TouchableOpacity
            style={styles.formattingButton}
            onPress={() => insertFormatting('heading')}
          >
            <Ionicons name="text" size={20} color={colors.text.primary} />
            <Text style={styles.formattingLabel}>标题</Text>
          </TouchableOpacity>
          
          <TouchableOpacity
            style={styles.formattingButton}
            onPress={() => insertFormatting('list')}
          >
            <Ionicons name="list" size={20} color={colors.text.primary} />
            <Text style={styles.formattingLabel}>列表</Text>
          </TouchableOpacity>
          
          <TouchableOpacity
            style={styles.formattingButton}
            onPress={() => insertFormatting('quote')}
          >
            <Ionicons name="chatbox" size={20} color={colors.text.primary} />
            <Text style={styles.formattingLabel}>引用</Text>
          </TouchableOpacity>
          
          <TouchableOpacity
            style={styles.formattingButton}
            onPress={() => setShowFormatting(false)}
          >
            <Ionicons name="close" size={20} color={colors.error} />
            <Text style={styles.formattingLabel}>关闭</Text>
          </TouchableOpacity>
        </View>
      </View>
    </Modal>
  );

  const renderCategoryModal = () => (
    <Modal
      visible={showCategoryModal}
      animationType="slide"
      presentationStyle="pageSheet"
    >
      <SafeAreaView style={styles.modalContainer}>
        <View style={styles.modalHeader}>
          <TouchableOpacity onPress={() => setShowCategoryModal(false)}>
            <Text style={styles.modalCancel}>取消</Text>
          </TouchableOpacity>
          <Text style={styles.modalTitle}>选择分类</Text>
          <TouchableOpacity onPress={() => setShowCategoryModal(false)}>
            <Text style={styles.modalDone}>完成</Text>
          </TouchableOpacity>
        </View>
        
        <ScrollView style={styles.modalContent}>
          <TouchableOpacity
            style={[styles.categoryOption, !categoryId && styles.categoryOptionSelected]}
            onPress={() => setCategoryId('')}
          >
            <Text style={[styles.categoryText, !categoryId && styles.categoryTextSelected]}>
              无分类
            </Text>
          </TouchableOpacity>
          
          {categories.map((category) => (
            <TouchableOpacity
              key={category.id}
              style={[styles.categoryOption, categoryId === category.id && styles.categoryOptionSelected]}
              onPress={() => setCategoryId(category.id)}
            >
              <Text style={[styles.categoryText, categoryId === category.id && styles.categoryTextSelected]}>
                {category.name}
              </Text>
              {category.description && (
                <Text style={styles.categoryDescription}>{category.description}</Text>
              )}
            </TouchableOpacity>
          ))}
        </ScrollView>
      </SafeAreaView>
    </Modal>
  );

  const renderTagModal = () => (
    <Modal
      visible={showTagModal}
      animationType="slide"
      presentationStyle="pageSheet"
    >
      <SafeAreaView style={styles.modalContainer}>
        <View style={styles.modalHeader}>
          <TouchableOpacity onPress={() => setShowTagModal(false)}>
            <Text style={styles.modalCancel}>取消</Text>
          </TouchableOpacity>
          <Text style={styles.modalTitle}>添加标签</Text>
          <TouchableOpacity onPress={handleAddTag}>
            <Text style={styles.modalDone}>添加</Text>
          </TouchableOpacity>
        </View>
        
        <View style={styles.modalContent}>
          <TextInput
            style={styles.tagInput}
            value={tagInput}
            onChangeText={setTagInput}
            placeholder="输入标签名称"
            autoFocus
            onSubmitEditing={handleAddTag}
          />
        </View>
      </SafeAreaView>
    </Modal>
  );

  return (
    <SafeAreaView style={styles.container}>
      {/* 顶部工具栏 */}
      <View style={styles.toolbar}>
        <TouchableOpacity onPress={handleExit}>
          <Ionicons name="close" size={24} color={colors.text.primary} />
        </TouchableOpacity>
        
        <View style={styles.toolbarCenter}>
          <Text style={styles.wordCount}>{wordCount} 字</Text>
        </View>
        
        <View style={styles.toolbarRight}>
          <TouchableOpacity
            style={styles.toolbarButton}
            onPress={handleUndo}
            disabled={currentHistoryIndex <= 0}
          >
            <Ionicons 
              name="arrow-undo" 
              size={20} 
              color={currentHistoryIndex <= 0 ? colors.text.secondary : colors.text.primary} 
            />
          </TouchableOpacity>
          
          <TouchableOpacity
            style={styles.toolbarButton}
            onPress={handleRedo}
            disabled={currentHistoryIndex >= history.length - 1}
          >
            <Ionicons 
              name="arrow-redo" 
              size={20} 
              color={currentHistoryIndex >= history.length - 1 ? colors.text.secondary : colors.text.primary} 
            />
          </TouchableOpacity>
          
          <TouchableOpacity
            style={[styles.saveButton, isSaving && styles.saveButtonDisabled]}
            onPress={handleSave}
            disabled={isSaving}
          >
            {isSaving ? (
              <ActivityIndicator size="small" color={colors.background.primary} />
            ) : (
              <Text style={styles.saveButtonText}>保存</Text>
            )}
          </TouchableOpacity>
        </View>
      </View>

      <KeyboardAvoidingView 
        style={styles.content} 
        behavior={Platform.OS === 'ios' ? 'padding' : 'height'}
      >
        <ScrollView style={styles.editorContainer}>
          {/* 标题输入 */}
          <TextInput
            style={styles.titleInput}
            value={title}
            onChangeText={setTitle}
            placeholder="输入文档标题"
            placeholderTextColor={colors.text.secondary}
            multiline
          />
          
          {/* 描述输入 */}
          <TextInput
            style={styles.descriptionInput}
            value={description}
            onChangeText={setDescription}
            placeholder="简短描述（可选）"
            placeholderTextColor={colors.text.secondary}
            multiline
          />
          
          {/* 分类和标签 */}
          <View style={styles.metaContainer}>
            <TouchableOpacity
              style={styles.metaButton}
              onPress={() => setShowCategoryModal(true)}
            >
              <Ionicons name="folder" size={16} color={colors.primary} />
              <Text style={styles.metaButtonText}>
                {categoryId ? categories.find(c => c.id === categoryId)?.name : '选择分类'}
              </Text>
            </TouchableOpacity>
            
            <TouchableOpacity
              style={styles.metaButton}
              onPress={() => setShowTagModal(true)}
            >
              <Ionicons name="pricetag" size={16} color={colors.primary} />
              <Text style={styles.metaButtonText}>添加标签</Text>
            </TouchableOpacity>
          </View>
          
          {/* 标签显示 */}
          {tags.length > 0 && (
            <View style={styles.tagsContainer}>
              {tags.map((tag, index) => (
                <View key={index} style={styles.tag}>
                  <Text style={styles.tagText}>{tag}</Text>
                  <TouchableOpacity onPress={() => handleRemoveTag(tag)}>
                    <Ionicons name="close" size={14} color={colors.primary} />
                  </TouchableOpacity>
                </View>
              ))}
            </View>
          )}
          
          {/* 内容输入 */}
          <TextInput
            ref={contentInputRef}
            style={styles.contentInput}
            value={content}
            onChangeText={handleContentChange}
            placeholder="开始编写内容..."
            placeholderTextColor={colors.text.secondary}
            multiline
            textAlignVertical="top"
            onSelectionChange={(event) => {
              setSelectionStart(event.nativeEvent.selection.start);
              setSelectionEnd(event.nativeEvent.selection.end);
            }}
          />
        </ScrollView>
      </KeyboardAvoidingView>

      {/* 底部格式化工具栏 */}
      <View style={styles.bottomToolbar}>
        <TouchableOpacity
          style={styles.bottomToolbarButton}
          onPress={() => setShowFormatting(true)}
        >
          <Ionicons name="text" size={24} color={colors.text.primary} />
        </TouchableOpacity>
        
        <TouchableOpacity style={styles.bottomToolbarButton}>
          <Ionicons name="image" size={24} color={colors.text.primary} />
        </TouchableOpacity>
        
        <TouchableOpacity style={styles.bottomToolbarButton}>
          <Ionicons name="link" size={24} color={colors.text.primary} />
        </TouchableOpacity>
        
        <TouchableOpacity 
          style={styles.bottomToolbarButton}
          onPress={() => navigation.navigate('DocumentReader' as any, { documentId: document?.id })}
          disabled={!document}
        >
          <Ionicons 
            name="eye" 
            size={24} 
            color={document ? colors.text.primary : colors.text.secondary} 
          />
        </TouchableOpacity>
      </View>

      {/* 模态框 */}
      {renderFormattingToolbar()}
      {renderCategoryModal()}
      {renderTagModal()}
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: colors.background.primary,
  },
  toolbar: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: spacing.lg,
    paddingVertical: spacing.md,
    borderBottomWidth: 1,
    borderBottomColor: colors.border,
  },
  toolbarCenter: {
    flex: 1,
    alignItems: 'center',
  },
  wordCount: {
    fontSize: typography.fontSize.sm,
    color: colors.text.secondary,
  },
  toolbarRight: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: spacing.sm,
  },
  toolbarButton: {
    padding: spacing.xs,
  },
  saveButton: {
    backgroundColor: colors.primary,
    paddingHorizontal: spacing.md,
    paddingVertical: spacing.sm,
    borderRadius: 6,
  },
  saveButtonDisabled: {
    opacity: 0.5,
  },
  saveButtonText: {
    color: colors.background.primary,
    fontSize: typography.fontSize.sm,
    fontWeight: typography.fontWeight.medium as any,
  },
  content: {
    flex: 1,
  },
  editorContainer: {
    flex: 1,
    padding: spacing.lg,
  },
  titleInput: {
    fontSize: typography.fontSize.xxl,
    fontWeight: typography.fontWeight.bold as any,
    color: colors.text.primary,
    marginBottom: spacing.lg,
    textAlignVertical: 'top',
  },
  descriptionInput: {
    fontSize: typography.fontSize.md,
    color: colors.text.secondary,
    marginBottom: spacing.lg,
    textAlignVertical: 'top',
  },
  metaContainer: {
    flexDirection: 'row',
    gap: spacing.sm,
    marginBottom: spacing.md,
  },
  metaButton: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: spacing.xs,
    paddingHorizontal: spacing.sm,
    paddingVertical: spacing.xs,
    backgroundColor: colors.background.secondary,
    borderRadius: 16,
  },
  metaButtonText: {
    fontSize: typography.fontSize.sm,
    color: colors.primary,
  },
  tagsContainer: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    gap: spacing.xs,
    marginBottom: spacing.lg,
  },
  tag: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: spacing.xs,
    backgroundColor: colors.primary + '20',
    paddingHorizontal: spacing.sm,
    paddingVertical: spacing.xs,
    borderRadius: 12,
  },
  tagText: {
    fontSize: typography.fontSize.xs,
    color: colors.primary,
  },
  contentInput: {
    fontSize: typography.fontSize.md,
    color: colors.text.primary,
    lineHeight: typography.fontSize.md * 1.6,
    minHeight: 400,
    textAlignVertical: 'top',
  },
  bottomToolbar: {
    flexDirection: 'row',
    justifyContent: 'space-around',
    paddingVertical: spacing.md,
    borderTopWidth: 1,
    borderTopColor: colors.border,
    backgroundColor: colors.background.secondary,
  },
  bottomToolbarButton: {
    padding: spacing.md,
  },
  formattingModal: {
    flex: 1,
    justifyContent: 'flex-end',
    backgroundColor: 'rgba(0,0,0,0.5)',
  },
  formattingToolbar: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    backgroundColor: colors.background.primary,
    padding: spacing.lg,
    gap: spacing.md,
  },
  formattingButton: {
    alignItems: 'center',
    padding: spacing.md,
    backgroundColor: colors.background.secondary,
    borderRadius: 8,
    minWidth: 60,
  },
  formattingLabel: {
    fontSize: typography.fontSize.xs,
    color: colors.text.primary,
    marginTop: spacing.xs,
  },
  modalContainer: {
    flex: 1,
    backgroundColor: colors.background.primary,
  },
  modalHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingHorizontal: spacing.lg,
    paddingVertical: spacing.md,
    borderBottomWidth: 1,
    borderBottomColor: colors.border,
  },
  modalTitle: {
    fontSize: typography.fontSize.lg,
    fontWeight: typography.fontWeight.semibold as any,
    color: colors.text.primary,
  },
  modalCancel: {
    fontSize: typography.fontSize.md,
    color: colors.text.secondary,
  },
  modalDone: {
    fontSize: typography.fontSize.md,
    color: colors.primary,
    fontWeight: typography.fontWeight.medium as any,
  },
  modalContent: {
    flex: 1,
    padding: spacing.lg,
  },
  categoryOption: {
    padding: spacing.md,
    borderRadius: 8,
    marginBottom: spacing.sm,
    backgroundColor: colors.background.secondary,
  },
  categoryOptionSelected: {
    backgroundColor: colors.primary + '20',
    borderWidth: 1,
    borderColor: colors.primary,
  },
  categoryText: {
    fontSize: typography.fontSize.md,
    color: colors.text.primary,
  },
  categoryTextSelected: {
    color: colors.primary,
    fontWeight: typography.fontWeight.medium as any,
  },
  categoryDescription: {
    fontSize: typography.fontSize.sm,
    color: colors.text.secondary,
    marginTop: spacing.xs,
  },
  tagInput: {
    backgroundColor: colors.background.secondary,
    borderRadius: 8,
    padding: spacing.md,
    fontSize: typography.fontSize.md,
    color: colors.text.primary,
  },
});