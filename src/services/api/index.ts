export { apiClient, default as ApiClient } from './client';
export { authService } from './authService';
export { recordingService } from './recordingService';
export { knowledgeService } from './knowledgeService';
export { writingService } from './writingService';

// API服务接口
export interface ApiService {
  get<T>(url: string, config?: any): Promise<T>;
  post<T>(url: string, data?: any, config?: any): Promise<T>;
  put<T>(url: string, data?: any, config?: any): Promise<T>;
  delete<T>(url: string, config?: any): Promise<T>;
  upload<T>(url: string, formData: FormData, config?: any): Promise<T>;
}
