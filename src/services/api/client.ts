import axios, {
  AxiosInstance,
  AxiosRequestConfig,
  AxiosResponse,
  AxiosError,
} from 'axios';
import { ApiError, ApiResponse } from '../../types';
import Toast from 'react-native-toast-message';
import { useAuthStore } from '../../stores';

class ApiClient {
  private client: AxiosInstance;

  constructor(baseURL: string = 'https://www.hjlingxi.com') {
    this.client = axios.create({
      baseURL,
      timeout: 10000,
      headers: {
        'Content-Type': 'application/json',
      },
    });

    this.setupInterceptors();
  }

  private setupInterceptors() {
    // 请求拦截器
    this.client.interceptors.request.use(
      (config) => {
        // 添加认证token
        const token = this.getAuthToken();
        if (token) {
          config.headers.Authorization = `Bearer ${token}`;
        }

        console.log(
          `🚀 API Request: ${config.method?.toUpperCase()} ${config.url}`
        );
        return config;
      },
      (error) => {
        console.error('❌ Request Error:', error);
        return Promise.reject(error);
      }
    );

    // 响应拦截器
    this.client.interceptors.response.use(
      (response: AxiosResponse) => {
        console.log(
          `✅ API Response: ${response.status} ${response.config.url}`
        );
        console.log('Response data:', JSON.stringify(response.data));
        return response;
      },
      (error: AxiosError) => {
        const apiError = this.handleError(error);
        console.error('❌ API Error:', apiError);

        // 显示错误提示
        Toast.show({
          type: 'error',
          text1: '请求失败',
          text2: apiError.message,
        });

        return Promise.reject(apiError);
      }
    );
  }

  private getAuthToken(): string | null {
    // 从auth store获取token
    const token = useAuthStore.getState().token;
    return token;
  }

  private handleError(error: AxiosError): ApiError {
    if (error.response) {
      // 服务器响应错误
      const status = error.response.status;
      const data = error.response.data as any;

      return {
        code: status.toString(),
        message: data?.message || this.getStatusMessage(status),
        details: data,
      };
    } else if (error.request) {
      // 网络错误
      return {
        code: 'NETWORK_ERROR',
        message: '网络连接失败，请检查网络设置',
      };
    } else {
      // 其他错误
      return {
        code: 'UNKNOWN_ERROR',
        message: error.message || '未知错误，请稍后重试',
      };
    }
  }

  private getStatusMessage(status: number): string {
    const statusMessages: Record<number, string> = {
      400: '请求参数错误',
      401: '未授权，请重新登录',
      403: '权限不足',
      404: '请求的资源不存在',
      500: '服务器内部错误',
      502: '网关错误',
      503: '服务暂时不可用',
    };

    return statusMessages[status] || '请求失败';
  }

  // 通用请求方法
  async get<T = any>(url: string, config?: AxiosRequestConfig): Promise<T> {
    const response = await this.client.get(url, config);
    return response.data;
  }

  async post<T = any>(
    url: string,
    data?: any,
    config?: AxiosRequestConfig
  ): Promise<T> {
    const response = await this.client.post(url, data, config);
    return response.data;
  }

  async put<T = any>(
    url: string,
    data?: any,
    config?: AxiosRequestConfig
  ): Promise<T> {
    const response = await this.client.put(url, data, config);
    return response.data;
  }

  async delete<T>(
    url: string,
    config?: AxiosRequestConfig
  ): Promise<ApiResponse<T>> {
    const response = await this.client.delete(url, config);
    return response.data;
  }

  // 文件上传
  async upload<T>(
    url: string,
    formData: FormData,
    config?: AxiosRequestConfig
  ): Promise<ApiResponse<T>> {
    const response = await this.client.post(url, formData, {
      ...config,
      headers: {
        'Content-Type': 'multipart/form-data',
        ...config?.headers,
      },
    });
    return response.data;
  }
}

// 创建默认实例
export const apiClient = new ApiClient();
export default ApiClient;
