import { useState, useEffect, useRef } from 'react';
import { Audio } from 'expo-av';
import * as FileSystem from 'expo-file-system';
import { useRecordingStore } from '../stores';
import { Recording } from '../types/recording';
import { generateId } from '../utils';
import Toast from 'react-native-toast-message';
import { useLocation, LocationData } from './useLocation';

export const useAudioRecorder = () => {
  const [recording, setRecording] = useState<Audio.Recording | null>(null);
  const [isRecording, setIsRecording] = useState(false);
  const [recordingDuration, setRecordingDuration] = useState(0);
  const [hasPermission, setHasPermission] = useState(false);
  const [audioLevel, setAudioLevel] = useState(0.5);
  const durationInterval = useRef<NodeJS.Timeout>();

  const { addRecording } = useRecordingStore();
  const { getCurrentLocation, hasPermission: hasLocationPermission, requestPermission: requestLocationPermission } = useLocation();

  // 检查和请求录音权限
  const requestPermission = async () => {
    try {
      const { granted } = await Audio.requestPermissionsAsync();
      setHasPermission(granted);

      if (!granted) {
        Toast.show({
          type: 'error',
          text1: '权限被拒绝',
          text2: '请在设置中允许应用访问麦克风',
        });
      }

      return { granted };
    } catch (error) {
      console.error('Failed to request permission', error);
      Toast.show({
        type: 'error',
        text1: '权限请求失败',
        text2: '无法请求麦克风权限',
      });
      return { granted: false };
    }
  };

  // 设置录音模式
  const setRecordingMode = async () => {
    try {
      await Audio.setAudioModeAsync({
        allowsRecordingIOS: true,
        playsInSilentModeIOS: true,
        shouldDuckAndroid: true,
        playThroughEarpieceAndroid: false,
        staysActiveInBackground: true, // 录音时保持活跃
        // 移除 interruptionMode 设置，使用默认值
      });
      console.log('Audio mode set to recording');
    } catch (error) {
      console.error('Failed to set recording audio mode:', error);
      throw error; // 重新抛出错误，让调用者知道设置失败
    }
  };

  // 重置音频模式到播放模式
  const resetAudioMode = async () => {
    try {
      await Audio.setAudioModeAsync({
        allowsRecordingIOS: false,
        playsInSilentModeIOS: true,
        shouldDuckAndroid: false,
        playThroughEarpieceAndroid: false,
        staysActiveInBackground: false, // 播放时不需要保持活跃
        // 移除 interruptionMode 设置，使用默认值
      });
      console.log('Audio mode reset to playback');
    } catch (error) {
      console.error('Failed to reset audio mode:', error);
      // 不抛出错误，因为重置失败不应该阻止其他操作
    }
  };

  useEffect(() => {
    // 初始化时检查权限
    requestPermission();

    // 设置初始音频模式为播放模式
    resetAudioMode();
  }, []);

  useEffect(() => {
    // 组件卸载时清理录音资源
    return () => {
      if (recording) {
        console.log('Cleaning up recording on component unmount');
        recording.stopAndUnloadAsync().catch(error => {
          console.error('Failed to cleanup recording on unmount:', error);
        });
      }

      // 清理计时器
      if (durationInterval.current) {
        clearInterval(durationInterval.current);
      }

      // 重置音频模式
      resetAudioMode().catch(error => {
        console.error('Failed to reset audio mode on unmount:', error);
      });
    };
  }, [recording]);

  useEffect(() => {
    // 更新录音时长（精确到毫秒）
    if (isRecording) {
      durationInterval.current = setInterval(() => {
        setRecordingDuration((prev) => prev + 0.1); // 100ms间隔
      }, 100);
    } else {
      if (durationInterval.current) {
        clearInterval(durationInterval.current);
      }
    }

    return () => {
      if (durationInterval.current) {
        clearInterval(durationInterval.current);
      }
    };
  }, [isRecording]);

  // 模拟音频电平变化（在真实实现中应该从录音状态获取）
  useEffect(() => {
    let levelInterval: NodeJS.Timeout;
    if (isRecording) {
      levelInterval = setInterval(() => {
        setAudioLevel(Math.random() * 0.8 + 0.2); // 0.2 到 1.0 之间
      }, 100);
    } else {
      setAudioLevel(0.5);
    }

    return () => {
      if (levelInterval) clearInterval(levelInterval);
    };
  }, [isRecording]);

  const startRecording = async () => {
    const startTime = Date.now();
    console.log('=== Starting recording process ===');

    try {
      // 检查权限
      if (!hasPermission) {
        console.log('Audio permission not granted, requesting...');
        const permission = await requestPermission();
        if (!permission.granted) {
          console.error('Audio permission denied by user');
          Toast.show({
            type: 'error',
            text1: '权限被拒绝',
            text2: '录音需要麦克风权限',
          });
          return;
        }
        console.log('Audio permission granted');
      }

      // 如果已经有录音对象，先完全清理
      if (recording) {
        console.log('Existing recording found, cleaning up...');
        try {
          const status = await recording.getStatusAsync();
          console.log('Existing recording status:', JSON.stringify(status, null, 2));

          if (status.canRecord || status.isRecording) {
            console.log('Stopping and unloading existing recording...');
            await recording.stopAndUnloadAsync();
            console.log('Existing recording cleaned up successfully');
          }
        } catch (cleanupError) {
          console.error('Error during recording cleanup:', cleanupError);
          // 继续执行，不让清理错误阻止新录音
        }

        setRecording(null);
        setIsRecording(false);
        setRecordingDuration(0);

        // 等待清理完成
        console.log('Waiting for cleanup to complete...');
        await new Promise(resolve => setTimeout(resolve, 200));
      }

      // 设置录音模式
      console.log('Setting audio mode for recording...');
      try {
        await setRecordingMode();
        console.log('Audio mode set successfully');
      } catch (modeError) {
        console.error('Failed to set audio mode:', modeError);
        Toast.show({
          type: 'error',
          text1: '音频设置失败',
          text2: '无法设置录音模式',
        });
        return;
      }

      // 创建录音配置
      const recordingOptions = Audio.RecordingOptionsPresets.HIGH_QUALITY;
      console.log('Using recording options:', JSON.stringify(recordingOptions, null, 2));

      // 创建新的录音实例
      console.log('Creating new recording instance...');
      const newRecording = new Audio.Recording();

      console.log('Preparing recording...');
      await newRecording.prepareToRecordAsync(recordingOptions);
      console.log('Recording prepared successfully');

      console.log('Starting recording...');
      await newRecording.startAsync();
      console.log('Recording started successfully');

      // 验证录音状态
      const status = await newRecording.getStatusAsync();
      console.log('Recording status after start:', JSON.stringify(status, null, 2));

      if (!status.isRecording) {
        throw new Error('Recording failed to start - status indicates not recording');
      }

      setRecording(newRecording);
      setIsRecording(true);
      setRecordingDuration(0);

      const elapsedTime = Date.now() - startTime;
      console.log(`Recording started successfully in ${elapsedTime}ms`);

      Toast.show({
        type: 'success',
        text1: '开始录音',
        text2: '正在录音中...',
      });

    } catch (error) {
      const elapsedTime = Date.now() - startTime;
      console.error(`Failed to start recording after ${elapsedTime}ms:`, error);
      console.error('Error stack:', error.stack);

      // 尝试重置音频模式
      try {
        await resetAudioMode();
        console.log('Audio mode reset after error');
      } catch (resetError) {
        console.error('Failed to reset audio mode after error:', resetError);
      }

      Toast.show({
        type: 'error',
        text1: '录音失败',
        text2: error.message || '无法开始录音，请检查麦克风权限',
      });

      // 确保状态重置
      setRecording(null);
      setIsRecording(false);
      setRecordingDuration(0);
    }
  };

  const stopRecording = async () => {
    const startTime = Date.now();
    console.log('=== Starting stop recording process ===');

    if (!recording) {
      console.warn('No recording instance to stop');
      return;
    }

    try {
      // 先设置状态，停止计时器
      setIsRecording(false);
      console.log('Recording state set to false');

      // 检查录音对象是否有效
      if (typeof recording.getStatusAsync !== 'function') {
        console.error('Recording object is invalid - missing getStatusAsync method');
        setRecording(null);
        setRecordingDuration(0);
        await resetAudioMode();
        return;
      }

      // 检查录音状态
      console.log('Getting recording status...');
      const status = await recording.getStatusAsync();
      console.log('Recording status before stop:', JSON.stringify(status, null, 2));

      // 获取录音文件信息（在停止前获取）
      const uri = recording.getURI();
      console.log('Recording URI before stop:', uri);

      if (!uri) {
        console.error('Recording URI is null - recording may have failed');
      }

      // 停止并卸载录音 - 关键步骤，必须正确执行
      console.log('Attempting to stop and unload recording...');
      // 只要录音对象存在，就尝试停止和卸载，不依赖状态检查
      await recording.stopAndUnloadAsync();
      console.log('Recording stopped and unloaded successfully');

      // 立即清空录音对象引用，释放麦克风
      setRecording(null);
      console.log('Recording object reference cleared');

      // 重置音频模式到播放模式，释放录音资源
      console.log('Resetting audio mode...');
      await resetAudioMode();
      console.log('Audio mode reset successfully');

      // 等待一小段时间确保资源完全释放
      console.log('Waiting for resource cleanup...');
      await new Promise(resolve => setTimeout(resolve, 100));

      // 检查文件是否存在和有效
      if (!uri) {
        console.warn('Recording URI is null, recording may have failed');
        setRecordingDuration(0);
        Toast.show({
          type: 'error',
          text1: '录音失败',
          text2: '录音文件未生成',
        });
        return;
      }

      // 等待文件系统同步
      await new Promise(resolve => setTimeout(resolve, 300));

      // 获取文件大小，使用重试机制确保文件完全写入
      let finalFileSize = 0;
      let fileExists = false;

      // 第一次检查
      let info = await FileSystem.getInfoAsync(uri);
      fileExists = info.exists;
      finalFileSize = info.exists && 'size' in info ? info.size : 0;
      console.log('Initial file check - exists:', fileExists, 'size:', finalFileSize);

      // 如果文件不存在或为空，等待并重试
      if (!fileExists || finalFileSize === 0) {
        console.log('File not ready, waiting and retrying...');
        await new Promise(resolve => setTimeout(resolve, 1000));

        info = await FileSystem.getInfoAsync(uri);
        fileExists = info.exists;
        finalFileSize = info.exists && 'size' in info ? info.size : 0;
        console.log('Retry file check - exists:', fileExists, 'size:', finalFileSize);

        // 如果仍然不存在或为空，再等待一次
        if (!fileExists || finalFileSize === 0) {
          console.log('File still not ready, final retry...');
          await new Promise(resolve => setTimeout(resolve, 1500));

          info = await FileSystem.getInfoAsync(uri);
          fileExists = info.exists;
          finalFileSize = info.exists && 'size' in info ? info.size : 0;
          console.log('Final file check - exists:', fileExists, 'size:', finalFileSize);
        }
      }

      // 最终检查文件是否有效
      if (!fileExists || finalFileSize === 0) {
        console.warn('Recording file is invalid after all retries');
        setRecordingDuration(0);
        Toast.show({
          type: 'error',
          text1: '录音失败',
          text2: '录音文件未能正确生成',
        });
        return;
      }

      // 获取位置信息
      console.log('开始获取位置信息...');
      let locationData: LocationData | null = null;
      try {
        if (!hasLocationPermission) {
          console.log('请求位置权限...');
          const result = await requestLocationPermission();
          console.log('位置权限结果:', result);
        }
        console.log('获取当前位置...');
        locationData = await getCurrentLocation();
        console.log('位置数据:', locationData);
      } catch (locationError) {
        console.warn('Failed to get location for recording:', locationError);
        // 位置获取失败不影响录音保存
      }

      // 创建录音记录
      const newRecording: Recording = {
        id: generateId(),
        title: `录音_${new Date().toLocaleString('zh-CN')}`,
        filePath: uri,
        duration: recordingDuration,
        size: finalFileSize,
        location: locationData ? {
          latitude: locationData.latitude,
          longitude: locationData.longitude,
          address: locationData.address,
          city: locationData.city,
          district: locationData.district,
          province: locationData.province,
          country: locationData.country,
        } : undefined,
        createdAt: new Date(),
        updatedAt: new Date(),
        userId: 'current-user',
      };

      console.log('Created recording object:', newRecording);

      // 保存到状态管理
      addRecording(newRecording);

      // 重置状态
      setRecordingDuration(0);

      Toast.show({
        type: 'success',
        text1: '录音完成',
        text2: `录音已保存，时长: ${Math.floor(recordingDuration)}秒${locationData ? ` · ${locationData.address || '位置已获取'}` : ''}`,
      });

      const elapsedTime = Date.now() - startTime;
      console.log(`Recording stopped and saved successfully in ${elapsedTime}ms`);
      return newRecording;

    } catch (error) {
      const elapsedTime = Date.now() - startTime;
      console.error(`Failed to stop recording after ${elapsedTime}ms:`, error);
      console.error('Error stack:', error.stack);

      // 即使出错，也要确保清理录音对象释放麦克风
      if (recording) {
        try {
          console.log('Attempting cleanup after error...');
          await recording.stopAndUnloadAsync();
          console.log('Cleanup successful after error');
        } catch (cleanupError) {
          console.error('Failed to cleanup recording on error:', cleanupError);
        }
      }

      // 重置音频模式
      try {
        console.log('Resetting audio mode after error...');
        await resetAudioMode();
        console.log('Audio mode reset successful after error');
      } catch (resetError) {
        console.error('Failed to reset audio mode after error:', resetError);
      }

      setRecording(null);
      setRecordingDuration(0);
      setIsRecording(false);

      Toast.show({
        type: 'error',
        text1: '保存失败',
        text2: error.message || '无法保存录音，请重试',
      });
    }
  };

  const pauseRecording = async () => {
    if (recording && isRecording) {
      try {
        await recording.pauseAsync();
        setIsRecording(false);
        Toast.show({
          type: 'info',
          text1: '录音已暂停',
        });
      } catch (error) {
        console.error('Failed to pause recording', error);
      }
    }
  };

  const resumeRecording = async () => {
    if (recording && !isRecording) {
      try {
        await recording.startAsync();
        setIsRecording(true);
        Toast.show({
          type: 'info',
          text1: '录音已继续',
        });
      } catch (error) {
        console.error('Failed to resume recording', error);
      }
    }
  };

  return {
    isRecording,
    recordingDuration,
    audioLevel,
    startRecording,
    stopRecording,
    pauseRecording,
    resumeRecording,
    hasPermission,
    requestPermission,
  };
};
