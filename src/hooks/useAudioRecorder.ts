import { useState, useEffect, useRef } from 'react';
import { Audio } from 'expo-av';
import * as FileSystem from 'expo-file-system';
import { useRecordingStore } from '../stores';
import { Recording } from '../types/recording';
import { generateId } from '../utils';
import Toast from 'react-native-toast-message';
import { useLocation } from './useLocation';

export const useAudioRecorder = () => {
  const [recording, setRecording] = useState<Audio.Recording | null>(null);
  const [isRecording, setIsRecording] = useState(false);
  const [recordingDuration, setRecordingDuration] = useState(0);
  const [hasPermission, setHasPermission] = useState(false);
  const [audioLevel, setAudioLevel] = useState(0.5);
  const durationInterval = useRef<NodeJS.Timeout>();

  const { addRecording } = useRecordingStore();
  const { getCurrentLocation, hasPermission: hasLocationPermission, requestPermission: requestLocationPermission } = useLocation();

  // 检查和请求录音权限
  const requestPermission = async () => {
    try {
      const { granted } = await Audio.requestPermissionsAsync();
      setHasPermission(granted);

      if (!granted) {
        Toast.show({
          type: 'error',
          text1: '权限被拒绝',
          text2: '请在设置中允许应用访问麦克风',
        });
      }

      return { granted };
    } catch (error) {
      console.error('Failed to request permission', error);
      Toast.show({
        type: 'error',
        text1: '权限请求失败',
        text2: '无法请求麦克风权限',
      });
      return { granted: false };
    }
  };

  useEffect(() => {
    // 初始化时检查权限
    requestPermission();

    // 设置音频模式
    Audio.setAudioModeAsync({
      allowsRecordingIOS: true,
      playsInSilentModeIOS: true,
      shouldDuckAndroid: true,
      playThroughEarpieceAndroid: false,
    });
  }, []);

  useEffect(() => {
    // 组件卸载时清理录音资源
    return () => {
      if (recording && typeof recording.unloadAsync === 'function') {
        console.log('Cleaning up recording on component unmount');
        recording.unloadAsync().catch(error => {
          console.error('Failed to cleanup recording on unmount:', error);
        });
      }
      
      // 清理计时器
      if (durationInterval.current) {
        clearInterval(durationInterval.current);
      }
    };
  }, [recording]);

  useEffect(() => {
    // 更新录音时长（精确到毫秒）
    if (isRecording) {
      durationInterval.current = setInterval(() => {
        setRecordingDuration((prev) => prev + 0.1); // 100ms间隔
      }, 100);
    } else {
      if (durationInterval.current) {
        clearInterval(durationInterval.current);
      }
    }

    return () => {
      if (durationInterval.current) {
        clearInterval(durationInterval.current);
      }
    };
  }, [isRecording]);

  // 模拟音频电平变化（在真实实现中应该从录音状态获取）
  useEffect(() => {
    let levelInterval: NodeJS.Timeout;
    if (isRecording) {
      levelInterval = setInterval(() => {
        setAudioLevel(Math.random() * 0.8 + 0.2); // 0.2 到 1.0 之间
      }, 100);
    } else {
      setAudioLevel(0.5);
    }

    return () => {
      if (levelInterval) clearInterval(levelInterval);
    };
  }, [isRecording]);

  const startRecording = async () => {
    try {
      console.log('Starting recording...');
      
      // 检查权限
      if (!hasPermission) {
        console.log('Requesting audio permission...');
        const permission = await requestPermission();
        if (!permission.granted) {
          return;
        }
      }

      // 如果已经有录音对象，先完全清理
      if (recording) {
        console.log('Cleaning up existing recording before starting new one');
        try {
          const status = await recording.getStatusAsync();
          console.log('Existing recording status:', status);
          if (status.isLoaded) {
            if (status.canRecord || status.isRecording) {
              await recording.stopAndUnloadAsync();
            } else {
              await recording.unloadAsync();
            }
          }
        } catch (cleanupError) {
          console.warn('Error during recording cleanup:', cleanupError);
        }
        setRecording(null);
        setIsRecording(false);
        setRecordingDuration(0);
        
        // 等待清理完成
        await new Promise(resolve => setTimeout(resolve, 200));
      }

      console.log('Creating new recording instance...');
      
      // 创建录音配置
      const recordingOptions = Audio.RecordingOptionsPresets.HIGH_QUALITY;
      console.log('Recording options:', recordingOptions);

      // 创建新的录音实例
      const newRecording = new Audio.Recording();
      console.log('Preparing recording...');
      await newRecording.prepareToRecordAsync(recordingOptions);
      
      console.log('Starting recording...');
      await newRecording.startAsync();
      
      // 验证录音状态
      const status = await newRecording.getStatusAsync();
      console.log('New recording status:', status);

      setRecording(newRecording);
      setIsRecording(true);
      setRecordingDuration(0);

      Toast.show({
        type: 'success',
        text1: '开始录音',
        text2: '正在录音中...',
      });
      
      console.log('Recording started successfully');
    } catch (error) {
      console.error('Failed to start recording', error);
      Toast.show({
        type: 'error',
        text1: '录音失败',
        text2: '无法开始录音，请检查麦克风权限',
      });
      
      // 确保状态重置
      setRecording(null);
      setIsRecording(false);
      setRecordingDuration(0);
    }
  };

  const stopRecording = async () => {
    if (!recording) {
      console.log('No recording to stop');
      return;
    }

    try {
      console.log('Stopping recording...');
      
      // 先设置状态，停止计时器
      setIsRecording(false);
      
      // 检查录音对象是否有效
      if (typeof recording.getStatusAsync !== 'function') {
        console.error('Recording object is invalid, cleaning up state');
        setRecording(null);
        setRecordingDuration(0);
        return;
      }
      
      // 检查录音状态
      const status = await recording.getStatusAsync();
      console.log('Recording status:', status);
      
      if (!status.isLoaded) {
        console.log('Recording is not loaded, cleaning up state');
        setRecording(null);
        setRecordingDuration(0);
        return;
      }

      // 获取录音文件信息（在停止前获取）
      const uri = recording.getURI();
      console.log('Recording URI:', uri);
      
      if (!uri) {
        // 即使URI为null，也要尝试清理录音对象
        console.warn('Recording URI is null, but still cleaning up');
        if (typeof recording.unloadAsync === 'function') {
          await recording.unloadAsync();
        }
        setRecording(null);
        setRecordingDuration(0);
        return;
      }

      // 停止并卸载录音 - 这是关键步骤
      if (status.isRecording) {
        console.log('Stopping active recording...');
        if (typeof recording.stopAndUnloadAsync === 'function') {
          await recording.stopAndUnloadAsync();
        }
      } else if (status.canRecord) {
        console.log('Unloading prepared recording...');
        if (typeof recording.unloadAsync === 'function') {
          await recording.unloadAsync();
        }
      } else {
        console.log('Force unloading recording...');
        if (typeof recording.unloadAsync === 'function') {
          await recording.unloadAsync();
        }
      }

      // 立即清空录音对象引用，释放麦克风
      setRecording(null);

      // 获取文件大小
      const info = await FileSystem.getInfoAsync(uri);
      const fileSize = info.exists && 'size' in info ? info.size : 0;
      console.log('File info:', info, 'File size:', fileSize);

      // 如果文件大小为0或文件不存在，说明录音失败
      if (!info.exists || fileSize === 0) {
        console.warn('Recording file does not exist or is empty');
        setRecordingDuration(0);
        Toast.show({
          type: 'error',
          text1: '录音失败',
          text2: '录音文件为空或不存在',
        });
        return;
      }

      // 获取位置信息
      console.log('开始获取位置信息...');
      let locationData = null;
      try {
        if (!hasLocationPermission) {
          console.log('请求位置权限...');
          const result = await requestLocationPermission();
          console.log('位置权限结果:', result);
        }
        console.log('获取当前位置...');
        locationData = await getCurrentLocation();
        console.log('位置数据:', locationData);
      } catch (locationError) {
        console.warn('Failed to get location for recording:', locationError);
        // 位置获取失败不影响录音保存
      }

      // 创建录音记录
      const newRecording: Recording = {
        id: generateId(),
        title: `录音_${new Date().toLocaleString('zh-CN')}`,
        filePath: uri,
        duration: recordingDuration,
        size: fileSize,
        location: locationData ? {
          latitude: locationData.latitude,
          longitude: locationData.longitude,
          address: locationData.address,
          city: locationData.city,
          district: locationData.district,
          province: locationData.province,
          country: locationData.country,
        } : undefined,
        createdAt: new Date(),
        updatedAt: new Date(),
        userId: 'current-user',
      };

      console.log('Created recording object:', newRecording);

      // 保存到状态管理
      addRecording(newRecording);

      // 重置状态
      setRecordingDuration(0);

      Toast.show({
        type: 'success',
        text1: '录音完成',
        text2: `录音已保存，时长: ${Math.floor(recordingDuration)}秒${locationData ? ` · ${locationData.address || '位置已获取'}` : ''}`,
      });

      return newRecording;
    } catch (error) {
      console.error('Failed to stop recording', error);
      
      // 即使出错，也要确保清理录音对象释放麦克风
      if (recording && typeof recording.unloadAsync === 'function') {
        try {
          await recording.unloadAsync();
        } catch (cleanupError) {
          console.error('Failed to cleanup recording on error:', cleanupError);
        }
      }
      
      setRecording(null);
      setRecordingDuration(0);
      setIsRecording(false);
      
      Toast.show({
        type: 'error',
        text1: '保存失败',
        text2: '无法保存录音，请重试',
      });
    }
  };

  const pauseRecording = async () => {
    if (recording && isRecording) {
      try {
        await recording.pauseAsync();
        setIsRecording(false);
        Toast.show({
          type: 'info',
          text1: '录音已暂停',
        });
      } catch (error) {
        console.error('Failed to pause recording', error);
      }
    }
  };

  const resumeRecording = async () => {
    if (recording && !isRecording) {
      try {
        await recording.startAsync();
        setIsRecording(true);
        Toast.show({
          type: 'info',
          text1: '录音已继续',
        });
      } catch (error) {
        console.error('Failed to resume recording', error);
      }
    }
  };

  return {
    isRecording,
    recordingDuration,
    audioLevel,
    startRecording,
    stopRecording,
    pauseRecording,
    resumeRecording,
    hasPermission,
    requestPermission,
  };
};
