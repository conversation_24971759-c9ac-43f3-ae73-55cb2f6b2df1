import { useState, useEffect } from 'react';
import * as Location from 'expo-location';
import Toast from 'react-native-toast-message';

export interface LocationData {
  latitude: number;
  longitude: number;
  address?: string;
  city?: string;
  district?: string;
  province?: string;
  country?: string;
}

export const useLocation = () => {
  const [location, setLocation] = useState<LocationData | null>(null);
  const [hasPermission, setHasPermission] = useState(false);
  const [isLoading, setIsLoading] = useState(false);

  // 请求位置权限
  const requestPermission = async () => {
    try {
      const { status } = await Location.requestForegroundPermissionsAsync();
      const granted = status === 'granted';
      setHasPermission(granted);

      if (!granted) {
        Toast.show({
          type: 'error',
          text1: '位置权限被拒绝',
          text2: '请在设置中允许应用访问位置信息',
        });
      }

      return { granted };
    } catch (error) {
      console.error('Failed to request location permission', error);
      Toast.show({
        type: 'error',
        text1: '位置权限请求失败',
        text2: '无法请求位置权限',
      });
      return { granted: false };
    }
  };

  // 获取当前位置
  const getCurrentLocation = async (): Promise<LocationData | null> => {
    try {
      setIsLoading(true);

      // 检查权限
      if (!hasPermission) {
        const permission = await requestPermission();
        if (!permission.granted) {
          return null;
        }
      }

      // 获取位置信息
      const locationResult = await Location.getCurrentPositionAsync({
        accuracy: Location.Accuracy.Balanced,
        timeInterval: 5000,
        distanceInterval: 1,
      });

      const { latitude, longitude } = locationResult.coords;

      // 反向地理编码
      const addressResult = await Location.reverseGeocodeAsync({
        latitude,
        longitude,
      });

      let locationData: LocationData = {
        latitude,
        longitude,
      };

      if (addressResult && addressResult.length > 0) {
        const addr = addressResult[0];
        locationData = {
          ...locationData,
          address: [
            addr.name,
            addr.street,
            addr.subregion,
            addr.region,
          ]
            .filter(Boolean)
            .join(', '),
          city: addr.city || addr.subregion || undefined,
          district: addr.district || addr.subregion || undefined,
          province: addr.region || undefined,
          country: addr.country || undefined,
        };
      }

      setLocation(locationData);
      return locationData;
    } catch (error) {
      console.error('Failed to get location', error);
      Toast.show({
        type: 'error',
        text1: '获取位置失败',
        text2: '无法获取当前位置信息',
      });
      return null;
    } finally {
      setIsLoading(false);
    }
  };

  // 格式化地址显示
  const formatAddress = (locationData: LocationData): string => {
    if (locationData.address) {
      return locationData.address;
    }

    const parts = [
      locationData.city,
      locationData.district,
      locationData.province,
    ].filter(Boolean);

    return parts.length > 0 ? parts.join(', ') : '未知位置';
  };

  // 组件初始化时检查权限
  useEffect(() => {
    (async () => {
      const { status } = await Location.getForegroundPermissionsAsync();
      setHasPermission(status === 'granted');
    })();
  }, []);

  return {
    location,
    hasPermission,
    isLoading,
    requestPermission,
    getCurrentLocation,
    formatAddress,
  };
};