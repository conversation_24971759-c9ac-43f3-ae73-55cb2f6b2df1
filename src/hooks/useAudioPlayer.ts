import { useState, useEffect } from 'react';
import { Audio } from 'expo-av';
import Toast from 'react-native-toast-message';

interface PlaybackStatus {
  isLoaded: boolean;
  isPlaying: boolean;
  isBuffering: boolean;
  durationMillis: number;
  positionMillis: number;
  rate: number;
}

export const useAudioPlayer = () => {
  const [sound, setSound] = useState<Audio.Sound | null>(null);
  const [playbackStatus, setPlaybackStatus] = useState<PlaybackStatus>({
    isLoaded: false,
    isPlaying: false,
    isBuffering: false,
    durationMillis: 0,
    positionMillis: 0,
    rate: 1.0,
  });
  const [playbackSpeed, setPlaybackSpeed] = useState(1.0);

  useEffect(() => {
    // 清理音频资源
    return () => {
      if (sound) {
        unload();
      }
    };
  }, []);

  const loadAudio = async (uri: string) => {
    try {
      // 如果已有音频加载，先卸载
      if (sound) {
        await unload();
      }

      // 创建Sound实例
      const { sound: newSound, status } = await Audio.Sound.createAsync(
        { uri },
        { shouldPlay: false, isLooping: false, rate: playbackSpeed },
        onPlaybackStatusUpdate
      );

      setSound(newSound);

      // 如果加载成功，更新状态
      if (status.isLoaded) {
        setPlaybackStatus({
          isLoaded: true,
          isPlaying: false,
          isBuffering: false,
          durationMillis: status.durationMillis || 0,
          positionMillis: status.positionMillis || 0,
          rate: status.rate || 1.0,
        });

        const durationText = status.durationMillis
          ? `${Math.floor(status.durationMillis / 60000)}:${Math.floor(
              (status.durationMillis % 60000) / 1000
            )
              .toString()
              .padStart(2, '0')}`
          : '未知';

        Toast.show({
          type: 'success',
          text1: '音频加载成功',
          text2: `时长: ${durationText}`,
        });
      }

      return newSound;
    } catch (error) {
      console.error('Failed to load audio', error);
      Toast.show({
        type: 'error',
        text1: '加载失败',
        text2: '无法加载音频文件',
      });
      return null;
    }
  };

  const onPlaybackStatusUpdate = (status: any) => {
    if (status.isLoaded) {
      setPlaybackStatus({
        isLoaded: true,
        isPlaying: status.isPlaying || false,
        isBuffering: status.isBuffering || false,
        durationMillis: status.durationMillis || 0,
        positionMillis: status.positionMillis || 0,
        rate: status.rate || 1.0,
      });
    }
  };

  const playPause = async () => {
    if (!sound) {
      Toast.show({
        type: 'error',
        text1: '播放错误',
        text2: '音频未加载',
      });
      return;
    }

    try {
      const status = await sound.getStatusAsync();

      if (status.isLoaded) {
        if (status.isPlaying) {
          await sound.pauseAsync();
        } else {
          await sound.playAsync();
        }
      }
    } catch (error) {
      console.error('Failed to play/pause', error);
      Toast.show({
        type: 'error',
        text1: '操作失败',
        text2: '无法播放/暂停音频',
      });
    }
  };

  const seekTo = async (position: number) => {
    if (!sound) return;

    try {
      await sound.setPositionAsync(position);
    } catch (error) {
      console.error('Failed to seek', error);
    }
  };

  const changeSpeed = async (speed: number) => {
    if (!sound) return;

    try {
      await sound.setRateAsync(speed, true);
      setPlaybackSpeed(speed);

      Toast.show({
        type: 'info',
        text1: '播放速度',
        text2: `${speed}x`,
      });
    } catch (error) {
      console.error('Failed to change speed', error);
      Toast.show({
        type: 'error',
        text1: '操作失败',
        text2: '无法改变播放速度',
      });
    }
  };

  const forward = async (seconds: number = 10) => {
    if (!sound) return;

    try {
      const status = await sound.getStatusAsync();
      if (status.isLoaded) {
        const newPosition = Math.min(
          (status.positionMillis || 0) + seconds * 1000,
          status.durationMillis || 0
        );
        await seekTo(newPosition);
      }
    } catch (error) {
      console.error('Failed to forward', error);
    }
  };

  const rewind = async (seconds: number = 10) => {
    if (!sound) return;

    try {
      const status = await sound.getStatusAsync();
      if (status.isLoaded) {
        const newPosition = Math.max(
          (status.positionMillis || 0) - seconds * 1000,
          0
        );
        await seekTo(newPosition);
      }
    } catch (error) {
      console.error('Failed to rewind', error);
    }
  };

  const stop = async () => {
    if (!sound) return;

    try {
      await sound.stopAsync();
      await sound.setPositionAsync(0);
    } catch (error) {
      console.error('Failed to stop', error);
    }
  };

  const unload = async () => {
    if (!sound) return;

    try {
      await sound.unloadAsync();
      setSound(null);
      setPlaybackStatus({
        isLoaded: false,
        isPlaying: false,
        isBuffering: false,
        durationMillis: 0,
        positionMillis: 0,
        rate: 1.0,
      });
      setPlaybackSpeed(1.0);
    } catch (error) {
      console.error('Failed to unload', error);
    }
  };

  return {
    sound,
    playbackStatus,
    playbackSpeed,
    loadAudio,
    playPause,
    seekTo,
    changeSpeed,
    forward,
    rewind,
    stop,
    unload,
  };
};
