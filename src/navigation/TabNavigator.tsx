import React from 'react';
import { createBottomTabNavigator } from '@react-navigation/bottom-tabs';
import { Ionicons } from '@expo/vector-icons';
import { colors } from '../styles';
import { TabParamList } from '../types/navigation';

// 导入页面组件
import HomeScreen from '../screens/home/<USER>';
import RecordingScreen from '../screens/recording/RecordingScreen';
import { KnowledgeScreen } from '../screens/knowledge/KnowledgeScreen';
import { WritingScreen } from '../screens/writing/WritingScreen';
import SettingsScreen from '../screens/settings/SettingsScreen';

const Tab = createBottomTabNavigator<TabParamList>();

export const TabNavigator: React.FC = () => {
  return (
    <Tab.Navigator
      screenOptions={({ route }) => ({
        tabBarIcon: ({ focused, color, size }) => {
          let iconName: keyof typeof Ionicons.glyphMap;

          switch (route.name) {
            case 'HomeTab':
              iconName = focused ? 'home' : 'home-outline';
              break;
            case 'RecordingTab':
              iconName = focused ? 'mic' : 'mic-outline';
              break;
            case 'KnowledgeTab':
              iconName = focused ? 'library' : 'library-outline';
              break;
            case 'WritingTab':
              iconName = focused ? 'create' : 'create-outline';
              break;
            case 'SettingsTab':
              iconName = focused ? 'settings' : 'settings-outline';
              break;
            default:
              iconName = 'help-outline';
          }

          return <Ionicons name={iconName} size={size} color={color} />;
        },
        tabBarActiveTintColor: colors.primary,
        tabBarInactiveTintColor: colors.text.secondary,
        tabBarStyle: {
          backgroundColor: colors.background.primary,
          borderTopColor: colors.separator,
          borderTopWidth: 1,
          paddingBottom: 5,
          paddingTop: 5,
          height: 60,
          elevation: 8,
          shadowColor: '#000',
          shadowOffset: {
            width: 0,
            height: -2,
          },
          shadowOpacity: 0.1,
          shadowRadius: 3,
        },
        tabBarLabelStyle: {
          fontSize: 12,
          fontWeight: '500',
        },
        tabBarItemStyle: {
          paddingVertical: 4,
        },
        headerStyle: {
          backgroundColor: colors.background.primary,
          borderBottomColor: colors.separator,
        },
        headerTintColor: colors.text.primary,
        headerTitleStyle: {
          fontWeight: '600',
          fontSize: 18,
        },
        headerShadowVisible: false,
      })}
    >
      <Tab.Screen
        name="HomeTab"
        component={HomeScreen}
        options={{
          title: '首页',
          headerShown: false,
        }}
      />
      <Tab.Screen
        name="RecordingTab"
        component={RecordingScreen}
        options={{
          title: '录音',
          headerShown: false,
          headerTitle: '智能录音',
        }}
      />
      <Tab.Screen
        name="KnowledgeTab"
        component={KnowledgeScreen}
        options={{
          title: '知识库',
          headerTitle: '知识库管理',
        }}
      />
      <Tab.Screen
        name="WritingTab"
        component={WritingScreen}
        options={{
          title: 'AI写作',
          headerTitle: 'AI智能写作',
        }}
      />
      <Tab.Screen
        name="SettingsTab"
        component={SettingsScreen}
        options={{
          title: '设置',
          headerTitle: '设置',
        }}
      />
    </Tab.Navigator>
  );
};
