import React from 'react';
import { NavigationContainer } from '@react-navigation/native';
import { createStackNavigator } from '@react-navigation/stack';
import { RootStackParamList } from '../types/navigation';
import { TabNavigator } from './TabNavigator';
import LoginScreen from '../screens/auth/LoginScreen';
import { RegisterScreen } from '../screens/auth/RegisterScreen';
import { ProfileEditScreen } from '../screens/settings/ProfileEditScreen';
import { NotificationSettingsScreen } from '../screens/settings/NotificationSettingsScreen';
import { LanguageSettingsScreen } from '../screens/settings/LanguageSettingsScreen';
import { StorageManagementScreen } from '../screens/settings/StorageManagementScreen';
import { FileUploadScreen } from '../screens/knowledge/FileUploadScreen';
import { FileManagementScreen } from '../screens/knowledge/FileManagementScreen';
import { CategoryManagementScreen } from '../screens/knowledge/CategoryManagementScreen';
import { SearchScreen } from '../screens/knowledge/SearchScreen';
import { DocumentShareScreen } from '../screens/knowledge/DocumentShareScreen';
import { DocumentCollaborationScreen } from '../screens/knowledge/DocumentCollaborationScreen';
import { DocumentReaderScreen } from '../screens/knowledge/DocumentReaderScreen';
import { DocumentEditorScreen } from '../screens/knowledge/DocumentEditorScreen';
import { colors } from '../styles';
import { useAuthStore } from '../stores';

const Stack = createStackNavigator<RootStackParamList>();

export const AppNavigator: React.FC = () => {
  const { isAuthenticated } = useAuthStore();

  return (
    <NavigationContainer>
      <Stack.Navigator
        screenOptions={{
          headerStyle: {
            backgroundColor: colors.background.primary,
            borderBottomColor: colors.border,
          },
          headerTintColor: colors.text.primary,
          headerTitleStyle: {
            fontWeight: '600',
          },
        }}
      >
        {!isAuthenticated ? (
          <>
            <Stack.Screen
              name="Login"
              component={LoginScreen}
              options={{ headerShown: false }}
            />
            <Stack.Screen
              name="Register"
              component={RegisterScreen}
              options={{ headerShown: false }}
            />
          </>
        ) : (
          <>
            <Stack.Screen
              name="Main"
              component={TabNavigator}
              options={{ headerShown: false }}
            />
            <Stack.Screen
              name="ProfileEdit"
              component={ProfileEditScreen}
              options={{ title: '编辑资料' }}
            />
            <Stack.Screen
              name="NotificationSettings"
              component={NotificationSettingsScreen}
              options={{ title: '通知设置' }}
            />
            <Stack.Screen
              name="LanguageSettings"
              component={LanguageSettingsScreen}
              options={{ title: '语言设置' }}
            />
            <Stack.Screen
              name="StorageManagement"
              component={StorageManagementScreen}
              options={{ title: '存储管理' }}
            />
            <Stack.Screen
              name="FileUpload"
              component={FileUploadScreen}
              options={{ headerShown: false }}
            />
            <Stack.Screen
              name="FileManagement"
              component={FileManagementScreen}
              options={{ headerShown: false }}
            />
            <Stack.Screen
              name="CategoryManagement"
              component={CategoryManagementScreen}
              options={{ headerShown: false }}
            />
            <Stack.Screen
              name="Search"
              component={SearchScreen}
              options={{ headerShown: false }}
            />
            <Stack.Screen
              name="DocumentShare"
              component={DocumentShareScreen}
              options={{ headerShown: false }}
            />
            <Stack.Screen
              name="DocumentCollaboration"
              component={DocumentCollaborationScreen}
              options={{ headerShown: false }}
            />
            <Stack.Screen
              name="DocumentReader"
              component={DocumentReaderScreen}
              options={{ headerShown: false }}
            />
            <Stack.Screen
              name="DocumentEditor"
              component={DocumentEditorScreen}
              options={{ headerShown: false }}
            />
          </>
        )}
      </Stack.Navigator>
    </NavigationContainer>
  );
};
