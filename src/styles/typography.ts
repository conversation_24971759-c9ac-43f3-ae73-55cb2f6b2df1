export const typography = {
  // 字体大小
  fontSize: {
    xs: 12,
    sm: 14,
    md: 16,
    base: 16,
    lg: 18,
    xl: 20,
    xxl: 28,
    '2xl': 24,
    '3xl': 30,
    '4xl': 36,
  },

  // 字体粗细
  fontWeight: {
    normal: '400' as const,
    medium: '500' as const,
    semibold: '600' as const,
    bold: '700' as const,
  },

  // 行高
  lineHeight: {
    tight: 1.2,
    normal: 1.5,
    relaxed: 1.75,
  },

  // 别名，为了兼容性
  get sizes() {
    return this.fontSize;
  },
  get weights() {
    return this.fontWeight;
  },
};
