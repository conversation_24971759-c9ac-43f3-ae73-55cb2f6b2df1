// 根导航参数类型
export type RootStackParamList = {
  Login: { phone?: string } | undefined;
  Register: undefined;
  Main: undefined;
  Recording: undefined;
  Knowledge: undefined;
  Writing: undefined;
  Settings: undefined;
  ProfileEdit: undefined;
  NotificationSettings: undefined;
  LanguageSettings: undefined;
  StorageManagement: undefined;
  FileUpload: undefined;
  FileManagement: undefined;
  CategoryManagement: undefined;
  Search: undefined;
  DocumentShare: { documentId: string };
  DocumentCollaboration: undefined;
  DocumentReader: { documentId: string };
  DocumentEditor: { documentId?: string };
};

// 底部标签导航参数类型
export type TabParamList = {
  HomeTab: undefined;
  RecordingTab: undefined;
  KnowledgeTab: undefined;
  WritingTab: undefined;
  SettingsTab: undefined;
};

// 录音模块导航参数类型
export type RecordingStackParamList = {
  RecordingHome: undefined;
  RecordingDetail: { recordingId: string };
  AudioPlayer: { recordingId: string };
};

// 知识库模块导航参数类型
export type KnowledgeStackParamList = {
  KnowledgeHome: undefined;
  DocumentDetail: { documentId: string };
  CategoryManage: undefined;
  FileUpload: undefined;
  FileManagement: undefined;
};

// AI写作模块导航参数类型
export type WritingStackParamList = {
  WritingHome: undefined;
  WritingEditor: { contentId?: string };
  WritingDetail: { contentId: string };
};
