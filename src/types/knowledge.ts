export interface KnowledgeDocument {
  id: string;
  title: string;
  content: string;
  description?: string;
  type: 'text' | 'audio' | 'pdf' | 'word' | 'image' | 'excel' | 'ppt' | 'other';
  size: number;
  filePath: string;
  categoryId?: string;
  tags: string[];
  summary?: string;
  createdAt: Date;
  updatedAt: Date;
  userId: string;
}

export interface KnowledgeCategory {
  id: string;
  name: string;
  description?: string;
  parentId?: string;
  order: number;
  color?: string;
  icon?: string;
  createdAt: Date;
  updatedAt: Date;
  userId: string;
}

export interface KnowledgeSearchResult {
  document: KnowledgeDocument;
  relevanceScore: number;
  matchedContent: string;
  highlights: string[];
}