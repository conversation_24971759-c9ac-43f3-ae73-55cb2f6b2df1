// 用户模型
export interface User {
  id: string;
  username: string;
  email: string;
  avatar?: string;
  createdAt: Date;
  updatedAt: Date;
}

// 录音模型
export interface Recording {
  id: string;
  title: string;
  filePath: string;
  duration: number;
  size: number;
  transcription?: string;
  summary?: string;
  createdAt: Date;
  updatedAt: Date;
  userId: string;
}

// 知识库文档模型
export interface KnowledgeDocument {
  id: string;
  title: string;
  content: string;
  type: 'text' | 'audio' | 'pdf' | 'word' | 'other';
  size: number;
  filePath: string;
  categoryId?: string;
  tags: string[];
  createdAt: Date;
  updatedAt: Date;
  userId: string;
}

// 知识库分类模型
export interface KnowledgeCategory {
  id: string;
  name: string;
  description?: string;
  parentId?: string;
  order: number;
  createdAt: Date;
  updatedAt: Date;
  userId: string;
}

// AI写作内容模型
export interface WritingContent {
  id: string;
  title: string;
  content: string;
  prompt: string;
  type: 'article' | 'optimization' | 'template';
  wordCount: number;
  createdAt: Date;
  updatedAt: Date;
  userId: string;
}

// API响应类型
export interface ApiResponse<T> {
  success: boolean;
  data: T;
  message?: string;
  error?: string;
}

// API错误类型
export interface ApiError {
  code: string;
  message: string;
  details?: any;
}
