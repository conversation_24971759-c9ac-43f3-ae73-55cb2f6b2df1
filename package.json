{"name": "mobile-office-assistant", "version": "1.0.0", "main": "index.ts", "scripts": {"start": "expo start", "android": "expo run:android", "ios": "expo run:ios", "web": "expo start --web", "lint": "eslint . --ext .ts,.tsx,.js,.jsx", "lint:fix": "eslint . --ext .ts,.tsx,.js,.jsx --fix", "format": "prettier --write \"**/*.{js,jsx,ts,tsx,json,css,md}\"", "type-check": "tsc --noEmit", "prepare": "husky"}, "dependencies": {"@expo/metro-runtime": "~4.0.0", "@expo/vector-icons": "~14.0.4", "@react-native-async-storage/async-storage": "1.23.1", "@react-navigation/bottom-tabs": "^6.6.1", "@react-navigation/native": "^6.1.7", "@react-navigation/stack": "^6.4.1", "axios": "^1.10.0", "expo": "~52.0.0", "expo-audio": "^0.3.5", "expo-av": "~15.0.2", "expo-blur": "~14.0.3", "expo-document-picker": "~13.0.3", "expo-file-system": "~18.0.12", "expo-image-picker": "~16.0.6", "expo-linear-gradient": "~14.0.2", "expo-location": "~18.0.10", "expo-sharing": "~13.0.1", "expo-status-bar": "~2.0.0", "jsencrypt": "^3.3.2", "react": "18.3.1", "react-dom": "18.3.1", "react-native": "0.76.9", "react-native-gesture-handler": "~2.20.2", "react-native-safe-area-context": "4.12.0", "react-native-screens": "4.4.0", "react-native-swipe-list-view": "^3.2.9", "react-native-toast-message": "^2.3.3", "react-native-url-polyfill": "^2.0.0", "react-native-web": "^0.19.12", "zustand": "^4.4.1"}, "devDependencies": {"@babel/core": "^7.25.2", "@types/react": "~18.3.12", "@types/react-native": "^0.73.0", "@typescript-eslint/eslint-plugin": "^6.21.0", "@typescript-eslint/parser": "^6.21.0", "babel-plugin-module-resolver": "^5.0.2", "eslint": "^8.57.1", "eslint-import-resolver-babel-module": "^5.3.2", "eslint-plugin-react": "^7.37.5", "eslint-plugin-react-hooks": "^5.2.0", "husky": "^9.1.7", "lint-staged": "^16.1.2", "prettier": "^3.6.2", "typescript": "~5.8.3"}, "private": true, "lint-staged": {"*.{js,jsx,ts,tsx}": ["eslint --fix", "prettier --write"], "*.{json,css,md}": ["prettier --write"]}}