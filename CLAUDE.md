# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.

## Development Commands

### React Native / Expo Development
```bash
# 进入项目目录
cd mobile-office-assistant

# Start the development server
npm start

# Run on specific platforms
npm run android  # Start on Android
npm run ios      # Start on iOS
npm run web      # Start on Web

# Install dependencies
npm install

# Clear cache and start fresh
npx expo start --clear
npx expo start -c  # 简写形式

# 重置并启动（解决缓存问题）
rm -rf node_modules package-lock.json && npm install && npx expo start -c
```

### Code Quality
```bash
# Linting (ESLint configured)
npx eslint src --ext .ts,.tsx

# Format code (Prettier configured)
npx prettier --write "src/**/*.{ts,tsx,js,jsx}"

# Type checking
npx tsc --noEmit
```

## Project Directory Structure
项目位于 `/mobile-office-assistant` 子目录下。所有开发工作都在这个目录中进行。

## Architecture Overview

### Project Structure
This is a React Native application built with Expo SDK 52, using TypeScript for type safety. The main application code is located in `/mobile-office-assistant/`.

### Navigation Architecture
The app uses React Navigation with a hierarchical structure:
- **Root**: Stack Navigator
- **Main**: Bottom Tab Navigator with 5 tabs (Home, Recording, Knowledge, Writing, Settings)
- **Screens**: Individual screen stacks for each tab

### State Management
Uses Zustand for global state with AsyncStorage persistence:
- `appStore`: Global app state (theme, language, loading states)
- `authStore`: Authentication and user state
- `recordingStore`: Recording-specific state
- `knowledgeStore`: Knowledge base state
- `writingStore`: AI writing state

### API Integration
Custom `ApiClient` class (`src/services/api/client.ts`) built on Axios:
- Automatic auth token injection
- Global error handling with toast notifications
- Support for standard REST operations and file uploads
- Base URL needs to be configured (currently placeholder)

### Styling System
Centralized design system in `src/styles/`:
- `colors.ts`: Color palette
- `typography.ts`: Font sizes and styles
- `spacing.ts`: Consistent spacing values
- `index.ts`: Combined exports

### Key Implementation Patterns

1. **Screen Components**: Located in `src/screens/`, each screen follows pattern:
   ```tsx
   export default function ScreenName() {
     // Screen implementation
   }
   ```

2. **API Calls**: Use the ApiClient instance:
   ```tsx
   import { apiClient } from '@/services/api/client';
   const response = await apiClient.get<ResponseType>('/endpoint');
   ```

3. **State Access**: Use Zustand hooks:
   ```tsx
   import { useAppStore } from '@/stores/appStore';
   const { isLoading, setLoading } = useAppStore();
   ```

4. **Navigation**: Use typed navigation hooks:
   ```tsx
   import { useNavigation } from '@react-navigation/native';
   const navigation = useNavigation<NavigationProp>();
   ```

### Important Configuration Files
- `tsconfig.json`: TypeScript config with path aliases (@/ -> src/)
- `.eslintrc.js`: ESLint rules for code quality
- `.prettierrc`: Code formatting rules
- `babel.config.js`: Module resolver for path aliases

### Key Dependencies and Versions
- **Expo SDK**: ~52.0.0
- **React Native**: 0.76.9
- **React**: 18.3.1
- **React Navigation**: 6.x
- **Zustand**: ^4.4.1
- **Axios**: ^1.10.0
- **AsyncStorage**: 1.23.1
- **TypeScript**: ~5.8.3

### Current Implementation Status
- Navigation structure: ✅ Complete
- Core components: ✅ Basic set implemented
- API client: ✅ Ready (needs endpoint configuration)
- State management: ✅ Infrastructure ready
- Authentication: ⚠️ Store exists but implementation pending
- Features: ⚠️ UI scaffolded, functionality pending
- Tests: ❌ Not yet implemented

### Development Tips
1. All text is currently in Chinese (UI is localized)
2. Use the existing component library in `src/components/common/`
3. Follow the established patterns for screens and navigation
4. API endpoints need to be configured before making actual requests
5. Toast notifications are automatically shown for API errors
6. Always run from the `/mobile-office-assistant` directory
7. Use `npx expo start -c` when encountering Metro bundler issues

### Common Issues and Solutions
1. **Metro Error 500**: Run `npx expo start -c` to clear cache
2. **Version Conflicts**: Check `package.json` for Expo-compatible versions
3. **Path Alias Issues**: Ensure babel.config.js is properly configured
4. **Android Build Issues**: Ensure Java 17 and Android SDK 35 are installed

### Project Requirements Reference
- See `/docs/requirements.md` for detailed feature requirements
- See `/docs/design.md` for architecture and design details
- See `/docs/product-requirements.md` for product specifications
- See `/docs/tasks.md` for implementation checklist